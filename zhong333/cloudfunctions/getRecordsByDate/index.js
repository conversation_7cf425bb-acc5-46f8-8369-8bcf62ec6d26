const cloud = require('wx-server-sdk')
cloud.init()

exports.main = async (event, context) => {
  const db = cloud.database()
  const wxContext = cloud.getWXContext()
  const { date } = event

  try {
    let whereClause = {
      // openid: wxContext.OPENID
    }

    if (/^\d{4}-\d{2}$/.test(date)) {
      // 传的是年月，如 2025-06，使用正则模糊匹配
      whereClause.date = db.RegExp({
        regexp: `^${date}`,
        options: 'i'
      })
    } else if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      // 传的是年月日，精确匹配
      whereClause.date = date
    } else {
      // 不符合格式，直接返回空或报错
      return {
        success: false,
        error: '日期格式错误，请传 YYYY-MM 或 YYYY-MM-DD'
      }
    }

    const res = await db.collection('records')
      .where(whereClause)
      .orderBy('date', 'desc')
      .get()

    return {
      success: true,
      data: res.data
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}