const cloud = require('wx-server-sdk')
cloud.init()

const db = cloud.database()

/**
 * 保存记录的云函数
 * 支持新增和更新记录
 */
exports.main = async (event, context) => {
  const { records, date } = event
  
  try {
    console.log('接收到保存请求:', { records, date })
    
    // 参数验证
    if (!records || !Array.isArray(records)) {
      return {
        success: false,
        message: '记录数据格式错误'
      }
    }
    
    if (!date) {
      return {
        success: false,
        message: '日期参数缺失'
      }
    }
    
    // 获取数据库集合
    const collection = db.collection('records')
    
    // 批量处理记录
    const results = []
    const errors = []

    // 使用事务确保数据一致性
    const transaction = await db.startTransaction()

    try {
      for (const record of records) {
        try {
          // 验证记录数据
          if (!record._id || !record.name || !record.date) {
            errors.push({
              record: record._id || 'unknown',
              error: '记录数据不完整'
            })
            continue
          }

          // 验证描述数据格式
          try {
            JSON.parse(record.description || '{}')
          } catch (jsonError) {
            errors.push({
              record: record._id,
              error: '描述数据格式错误'
            })
            continue
          }

          // 准备保存的数据
          const saveData = {
            _id: record._id,
            name: record.name,
            date: record.date,
            description: record.description || '{}',
            updateTime: new Date().toISOString()
          }

          // 如果是新记录，添加创建时间
          if (record.createTime) {
            saveData.createTime = record.createTime
          } else {
            saveData.createTime = new Date().toISOString()
          }

          // 检查记录是否已存在
          const existingRecord = await transaction.collection('records').doc(record._id).get()

          let operation = 'create'
          if (existingRecord.data.length > 0) {
            operation = 'update'
          }

          // 保存记录
          await transaction.collection('records').doc(record._id).set(saveData)

          results.push({
            _id: record._id,
            success: true,
            operation: operation
          })

          console.log(`记录 ${record._id} ${operation === 'create' ? '创建' : '更新'}成功`)

        } catch (recordError) {
          console.error(`保存记录 ${record._id} 失败:`, recordError)
          errors.push({
            record: record._id || 'unknown',
            error: recordError.message
          })
        }
      }

      // 如果有错误，回滚事务
      if (errors.length > 0) {
        await transaction.rollback()
        console.log('存在错误，事务已回滚')
      } else {
        // 提交事务
        await transaction.commit()
        console.log('所有记录保存成功，事务已提交')
      }

    } catch (transactionError) {
      await transaction.rollback()
      throw transactionError
    }
    
    // 返回结果
    const response = {
      success: errors.length === 0,
      message: errors.length === 0 ? '保存成功' : `部分记录保存失败`,
      data: {
        total: records.length,
        success: results.length,
        failed: errors.length,
        results: results,
        errors: errors
      }
    }
    
    console.log('保存结果:', response)
    return response
    
  } catch (error) {
    console.error('云函数执行失败:', error)
    return {
      success: false,
      message: '服务器错误: ' + error.message,
      error: error.toString()
    }
  }
}
