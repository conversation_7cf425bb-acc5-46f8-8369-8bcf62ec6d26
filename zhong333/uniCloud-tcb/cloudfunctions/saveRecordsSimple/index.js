const cloud = require('wx-server-sdk')
cloud.init()

const db = cloud.database()

/**
 * 保存记录的云函数 - 简化版本
 * 支持新增和更新记录
 */
exports.main = async (event, context) => {
  const { records, date } = event
  
  try {
    console.log('接收到保存请求:', { recordsCount: records?.length, date })
    
    // 参数验证
    if (!records || !Array.isArray(records)) {
      return {
        success: false,
        message: '记录数据格式错误'
      }
    }
    
    if (!date) {
      return {
        success: false,
        message: '日期参数缺失'
      }
    }
    
    // 获取数据库集合
    const collection = db.collection('records')
    
    // 批量处理记录
    const results = []
    const errors = []
    
    for (const record of records) {
      try {
        // 验证记录数据
        if (!record._id || !record.name || !record.date) {
          errors.push({
            record: record._id || 'unknown',
            error: '记录数据不完整，缺少必要字段'
          })
          continue
        }
        
        // 验证描述数据格式
        try {
          JSON.parse(record.description || '{}')
        } catch (jsonError) {
          errors.push({
            record: record._id,
            error: '描述数据不是有效的JSON格式'
          })
          continue
        }
        
        // 准备保存的数据
        const saveData = {
          name: record.name,
          date: record.date,
          description: record.description || '{}',
          updateTime: new Date().toISOString()
        }
        
        // 如果是新记录，添加创建时间
        if (record.createTime) {
          saveData.createTime = record.createTime
        } else {
          saveData.createTime = new Date().toISOString()
        }
        
        // 使用set方法，如果记录不存在则创建，存在则更新
        await collection.doc(record._id).set(saveData)
        
        results.push({
          _id: record._id,
          success: true,
          name: record.name,
          date: record.date
        })
        
        console.log(`记录 ${record._id} (${record.name}) 保存成功`)
        
      } catch (recordError) {
        console.error(`保存记录 ${record._id} 失败:`, recordError)
        errors.push({
          record: record._id || 'unknown',
          error: recordError.message || '未知错误'
        })
      }
    }
    
    // 返回结果
    const response = {
      success: errors.length === 0,
      message: errors.length === 0 ? 
        `成功保存 ${results.length} 条记录` : 
        `保存完成，成功 ${results.length} 条，失败 ${errors.length} 条`,
      data: {
        date: date,
        total: records.length,
        success: results.length,
        failed: errors.length,
        results: results,
        errors: errors
      }
    }
    
    console.log('保存结果:', {
      success: response.success,
      total: response.data.total,
      successCount: response.data.success,
      failedCount: response.data.failed
    })
    
    return response
    
  } catch (error) {
    console.error('云函数执行失败:', error)
    return {
      success: false,
      message: '服务器内部错误',
      error: error.message || error.toString(),
      data: {
        total: 0,
        success: 0,
        failed: 0,
        results: [],
        errors: []
      }
    }
  }
}
