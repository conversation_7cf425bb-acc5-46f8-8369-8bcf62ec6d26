# saveRecordsSimple 云函数

## 功能说明
保存记账记录的云函数，支持批量新增和更新记录。

## 输入参数
```javascript
{
  records: [
    {
      _id: "zhong333-2025-06-27",        // 记录唯一标识
      name: "zhong333",                  // 用户名
      date: "2025-06-27",               // 日期
      description: '{"面包":105.29,"外卖":45}', // 记账内容JSON字符串
      createTime: "2025-06-27T10:00:00.000Z",  // 创建时间（可选）
      updateTime: "2025-06-27T10:00:00.000Z"   // 更新时间（可选）
    }
  ],
  date: "2025-06-27"                    // 操作日期
}
```

## 返回结果
```javascript
{
  success: true,                       // 是否成功
  message: "成功保存 2 条记录",          // 结果消息
  data: {
    date: "2025-06-27",                // 操作日期
    total: 2,                          // 总记录数
    success: 2,                        // 成功数量
    failed: 0,                         // 失败数量
    results: [                         // 成功结果
      {
        _id: "zhong333-2025-06-27",
        success: true,
        name: "zhong333",
        date: "2025-06-27"
      }
    ],
    errors: []                         // 错误信息
  }
}
```

## 数据验证
- 必须字段：`_id`, `name`, `date`
- `description` 必须是有效的JSON字符串
- 自动添加/更新 `createTime` 和 `updateTime`

## 使用示例
```javascript
const res = await wx.cloud.callFunction({
  name: 'saveRecordsSimple',
  data: {
    records: updatedRecords,
    date: '2025-06-27'
  }
});

if (res.result.success) {
  console.log('保存成功');
} else {
  console.log('保存失败:', res.result.message);
}
```
