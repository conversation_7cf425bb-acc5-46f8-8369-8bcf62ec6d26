"use strict";const e=require("../../common/vendor.js"),t={__name:"index",setup(t){const o=e.ref([]),a=e.ref(""),n=e.ref(""),r=e.ref("month");function l(e){try{const t=JSON.parse(e);console.log(t);const o=Object.values(t).reduce(((e,t)=>{const o=parseFloat(t);return e+(isNaN(o)?0:o)}),0);return Number(o.toFixed(2))}catch(t){return 0}}function s(e){const t=e.reduce(((e,t)=>e+l(t.description)),0);return Number(t.toFixed(2))}function c(e,t=!1){if(t){const[t,o]=e.split("-");return`${t}年${["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"][parseInt(o)-1]}`}const o=new Date(e),a=new Date,n=new Date(a);n.setDate(n.getDate()-1);const r=`${(o.getMonth()+1).toString().padStart(2,"0")}-${o.getDate().toString().padStart(2,"0")}`,l=["周日","周一","周二","周三","周四","周五","周六"][o.getDay()];return e===m(a)?`${r} 今天 ${l}`:e===m(n)?`${r} 昨天 ${l}`:`${r} ${l}`}function u(e){const t=new Set;return e.forEach((e=>{e.name&&t.add(e.name)})),Array.from(t)}function d(e){const t=new Set;return e.forEach((e=>{e.date&&t.add(e.date)})),Array.from(t)}function i(e){const t={};return e.forEach((e=>{const o=e.name||"未知用户",a=l(e.description);t[o]||(t[o]={name:o,total:0,count:0}),t[o].total+=a,t[o].count+=1})),Object.values(t).map((e=>({...e,total:e.total.toFixed(2)}))).sort(((e,t)=>parseFloat(t.total)-parseFloat(e.total)))}function m(e){return`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")}`}function g(e,t){const o=new Date(e,t,0).getDate(),a=[];for(let n=1;n<=o;n++){const o=new Date(e,t-1,n);a.push({date:m(o)})}return a}const h=new Date;function v(e){r.value=e,console.log("切换视图模式:",e),y("year"===e?n.value:a.value)}function f(e){const t=e.detail.value;if(console.log("选择日期:",t,"当前模式:",r.value),"year"===r.value)n.value=t,y(n.value);else{a.value=t;const[e,n]=t.split("-").map(Number);o.value=g(e,n),y(a.value)}}async function y(t){try{console.log("开始加载数据:",t,"当前视图模式:",r.value),e.index.showLoading({title:"加载中..."});const a=await e.wx$1.cloud.callFunction({name:"getRecordsByDate",data:{date:t}});if(e.index.hideLoading(),a.result.success)if(console.log("云函数返回结果:",a.result),a.result.isYearQuery&&"year"===r.value){const t=a.result.monthlyData||[];console.log("处理年度数据:",t),o.value=t.map((e=>({date:e.month,records:e.records,totalAmount:e.totalAmount,recordCount:e.records.length,isMonthSummary:!0}))),console.log("年度视图数据:",o.value),0===o.value.length&&e.index.showToast({title:"该年暂无记录",icon:"none"})}else{const e=a.result.data,n={};console.log("处理月度数据:",e),e.forEach((e=>{n[e.date]||(n[e.date]=[]),n[e.date].push(e)}));const[r,l]=t.split("-").map(Number),s=g(r,l);o.value=s.map((e=>({date:e.date,records:n[e.date]||[],isMonthSummary:!1}))),console.log("月度视图数据:",o.value)}else console.log("查询失败:",a.result),e.index.showToast({title:a.result.error||"查询失败",icon:"none"})}catch(a){console.log("调用失败:",a),e.index.hideLoading(),e.index.showToast({title:"网络错误，请重试",icon:"none"})}}function p(t){var o,a;t.isMonthSummary?(a=t.date,e.index.navigateTo({url:`/pages/month/month?month=${a}`})):(o=t.date,e.index.navigateTo({url:`/pages/days/day?date=${o}`}))}return a.value=`${h.getFullYear()}-${(h.getMonth()+1).toString().padStart(2,"0")}`,n.value=`${h.getFullYear()}`,o.value=g(h.getFullYear(),h.getMonth()+1),console.log("初始化:",{selectedMonth:a.value,selectedYear:n.value}),e.onShow((()=>{console.log("页面显示，重新加载数据"),"year"===r.value?y(n.value):y(a.value)})),(t,m)=>({a:"month"===r.value?1:"",b:e.o((e=>v("month"))),c:"year"===r.value?1:"",d:e.o((e=>v("year"))),e:e.t("year"===r.value?n.value+"年":"选择月份："+a.value),f:"year"===r.value?"year":"month",g:e.o(f),h:e.f(o.value,((t,o,a)=>e.e({a:e.t(c(t.date,t.isMonthSummary)),b:t.isMonthSummary&&t.recordCount},t.isMonthSummary&&t.recordCount?{c:e.t(t.recordCount)}:{},{d:t.records&&t.records.length>0},t.records&&t.records.length>0?{e:e.t(t.isMonthSummary?t.totalAmount:s(t.records))}:{},{f:t.isMonthSummary&&t.records&&t.records.length>0},t.isMonthSummary&&t.records&&t.records.length>0?{g:e.t(t.recordCount),h:e.t(u(t.records).length),i:e.t(d(t.records).length),j:e.f(i(t.records),((t,o,a)=>({a:e.t(t.name),b:e.t(t.total),c:t.name})))}:!t.isMonthSummary&&t.records&&t.records.length>0?{l:e.f(t.records,((t,o,a)=>({a:e.t(t.name),b:e.t(l(t.description)),c:o})))}:{},{k:!t.isMonthSummary&&t.records&&t.records.length>0,m:o,n:e.o((e=>p(t)),o),o:t.records&&t.records.length>0?1:""})))})}};wx.createPage(t);
