"use strict";const e=require("../../common/vendor.js"),l={__name:"day",props:["date"],setup(l){const a=l,t=e.ref([]);async function o(l){console.log("加载数据:",l);try{const a=await e.wx$1.cloud.callFunction({name:"getRecordsByDate",data:{date:l}});if(a.result.success){const e=a.result.data;console.log("查询成功:",e),t.value=e,d(),console.log("parsedDescriptions:",t.value,u.value)}else console.log("查询失败:",a.result),e.index.showToast({title:"查询失败",icon:"none"})}catch(a){console.log("调用失败:",a),e.index.showToast({title:"调用失败",icon:"none"})}}if(a.date)o(a.date);else{o((new Date).toISOString().split("T")[0])}const u=e.computed((()=>t.value.map((e=>{try{return JSON.parse(e.description||"{}")}catch{return{}}})))),n=e.ref([]),i=e.ref([]),s=e.ref([]),v=e.ref([]),d=()=>{const e=t.value.length;n.value=u.value.map((e=>Object.fromEntries(Object.keys(e).map((e=>[e,e]))))),i.value=Array(e).fill(""),s.value=Array(e).fill(0),v.value=Array(e).fill({}),c.value=Array(e).fill(!1),f.value=Array(e).fill({}),g.value=Array(e).fill(!1),console.log("编辑状态初始化完成，记录数量:",e)},r=(e,l)=>{var a,t,o;const i=n.value[e][l],s=u.value[e][l];if(null==(a=v.value[e])?void 0:a[l])if(0===Number(s))delete u.value[e][l],delete n.value[e][l];else if(i!==l&&i.trim())return u.value[e][i]=s,delete u.value[e][l],n.value[e][i]=i,delete n.value[e][l],void delete v.value[e][l];v.value[e]={...v.value[e],[l]:!(null==(t=v.value[e])?void 0:t[l])},(null==(o=v.value[e])?void 0:o[l])||(f.value[e]={...f.value[e],[l]:!1})},c=e.ref([]),m=e.ref(["地铁","早饭","午饭","晚饭"]),f=e.ref([]),g=e.ref([]),p=async()=>{var l;try{e.index.showLoading({title:"保存中..."});const o=t.value.map(((e,l)=>({...e,description:JSON.stringify(u.value[l]),updateTime:(new Date).toISOString()})));console.log("准备保存的数据：",o),console.log("第一条记录详情：",JSON.stringify(o[0],null,2));const n=await e.wx$1.cloud.callFunction({name:"postRecordsByDate",data:{records:o,date:a.date}});e.index.hideLoading(),n.result&&n.result.success?(t.value=o,e.index.showToast({title:"保存成功",icon:"success",duration:2e3}),console.log("保存成功：",n.result)):(console.log("保存失败:",n.result),e.index.showToast({title:(null==(l=n.result)?void 0:l.message)||"保存失败",icon:"none"}))}catch(o){console.log("保存出错:",o),e.index.hideLoading(),e.index.showToast({title:"网络错误，请重试",icon:"none"})}},h=()=>{const l=t.value.map((e=>e.name)),o=[{name:"zhong333"},{name:"YPP"}].filter((e=>!l.includes(e.name)));if(0===o.length)return void e.index.showToast({title:"所有用户都已添加",icon:"none",duration:2e3});const u=o.map((e=>e.name));e.index.showActionSheet({itemList:u,success:l=>{const u=o[l.tapIndex],n={_id:`${u.name}-${a.date}`,name:u.name,date:a.date||(new Date).toISOString().split("T")[0],description:"{}",createTime:(new Date).toISOString(),updateTime:(new Date).toISOString()};t.value.push(n),d(),console.log("新增用户记录:",n),e.index.showToast({title:`新增${u.name}成功`,icon:"none",duration:2e3})},fail:()=>{console.log("用户取消选择")}})},w=e=>{const l=null==u?void 0:u.value[e];return u.value.length>0?Object.values(l).reduce(((e,l)=>e+Number(l||0)),0).toFixed(2):0},x=e.computed((()=>u.value.reduce(((e,l)=>e+Object.values(l).reduce(((e,l)=>e+Number(l||0)),0)),0).toFixed(2)));return(l,o)=>({a:e.t(a.date||"今日"),b:e.f(t.value,((l,a,t)=>e.e({a:e.t(l.name),b:e.f(u.value[a],((l,t,o)=>{var i,s,d,c,g,p;return e.e({a:null==(i=v.value[a])?void 0:i[t]},(null==(s=v.value[a])?void 0:s[t])?e.e({b:e.o((e=>((e,l)=>{f.value[e]={...f.value[e],[l]:!0}})(a,t)),t),c:e.o((e=>((e,l)=>{setTimeout((()=>{f.value[e]={...f.value[e],[l]:!1}}),200)})(a,t)),t),d:n.value[a][t],e:e.o((e=>n.value[a][t]=e.detail.value),t),f:null==(d=f.value[a])?void 0:d[t]},(null==(c=f.value[a])?void 0:c[t])?{g:e.f(m.value,((l,o,u)=>({a:e.t(l),b:l,c:e.o((e=>((e,l,a)=>{n.value[e][l]=a,f.value[e]={...f.value[e],[l]:!1}})(a,t,l)),l)})))}:{}):{h:e.t(t)},{i:null==(g=v.value[a])?void 0:g[t]},(null==(p=v.value[a])?void 0:p[t])?{j:t,k:u.value[a][t],l:e.o((e=>u.value[a][t]=e.detail.value),t),m:e.o((e=>r(a,t)),t)}:{n:e.t(u.value[a][t]),o:e.o((e=>r(a,t)),t)},{p:t})})),c:!c.value[a]},c.value[a]?e.e({e:e.o((e=>g.value[a]=!0),l._id),f:e.o((e=>(e=>{setTimeout((()=>{g.value[e]=!1}),200)})(a)),l._id),g:i.value[a],h:e.o((e=>i.value[a]=e.detail.value),l._id),i:g.value[a]},g.value[a]?{j:e.f(m.value,((l,t,o)=>({a:e.t(l),b:l,c:e.o((e=>((e,l)=>{i.value[e]=l,g.value[e]=!1})(a,l)),l)})))}:{},{k:s.value[a],l:e.o((e=>s.value[a]=e.detail.value),l._id),m:e.o((l=>(l=>{var a;const t=null==(a=i.value[l])?void 0:a.trim(),o=s.value[l];t?isNaN(o)?e.index.showToast({title:"金额必须是数字",icon:"none"}):(u.value[l][t]=o,i.value[l]="",s.value[l]=0,c.value[l]=!1):e.index.showToast({title:"项目名称不能为空",icon:"none"})})(a)),l._id)}):{d:e.o((e=>(e=>{c.value[e]=!0})(a)),l._id)},{n:e.t(w(a)),o:l._id}))),c:e.t(x.value),d:e.o(h),e:e.o(p)})}};wx.createPage(l);
