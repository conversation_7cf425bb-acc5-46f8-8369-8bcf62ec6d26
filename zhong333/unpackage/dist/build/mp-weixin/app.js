"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const o=require("./common/vendor.js");Math;const e={onLaunch:function(){if(console.log("App Launch"),!o.wx$1.cloud)return void console.error("请使用 2.2.3 及以上基础库以支持云开发");o.wx$1.cloud.init({env:"cloudbase-4gc39cjdb0cbb5e2",traceUser:!0});const e=o.wx$1.getStorageSync("openid");e?console.log("缓存的openid:",e):o.wx$1.cloud.callFunction({name:"getOpenId",success:e=>{o.wx$1.setStorageSync("openid",e.result.openid)},fail:o=>{console.error("获取openid失败",o)}})},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")}};function n(){return{app:o.createSSRApp(e)}}n().app.mount("#app"),exports.createApp=n;
