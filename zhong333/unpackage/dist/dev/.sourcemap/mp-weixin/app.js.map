{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\nexport default {\r\n  onLaunch: function() {\r\n    console.log('App Launch')\r\n    if (!wx.cloud) {\r\n      console.error('请使用 2.2.3 及以上基础库以支持云开发')\r\n      return\r\n    }\r\n    wx.cloud.init({\r\n      env: 'cloudbase-4gc39cjdb0cbb5e2',\r\n      traceUser: true,\r\n    })\r\n\r\n    const cachedOpenId = wx.getStorageSync('openid')\r\n    if (!cachedOpenId) {\r\n      wx.cloud.callFunction({\r\n        name: 'getOpenId',\r\n        success: res => {\r\n          wx.setStorageSync('openid', res.result.openid)\r\n        },\r\n        fail: err => {\r\n          console.error('获取openid失败', err)\r\n        }\r\n      })\r\n    } else {\r\n      console.log('缓存的openid:', cachedOpenId)\r\n    }\r\n  },\r\n  onShow: function() {\r\n    console.log('App Show')\r\n  },\r\n  onHide: function() {\r\n    console.log('App Hide')\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n</style>\r\n", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nexport function createApp() {\n  const app = createSSRApp(App)\n  return {\n    app\n  }\n}\n// #endif"], "names": ["uni", "wx", "createSSRApp", "App"], "mappings": ";;;;;;;;AACA,MAAK,YAAU;AAAA,EACb,UAAU,WAAW;AACnBA,kBAAAA,MAAY,MAAA,OAAA,gBAAA,YAAY;AACxB,QAAI,CAACC,cAAE,KAAC,OAAO;AACbD,oBAAAA,MAAA,MAAA,SAAA,gBAAc,wBAAwB;AACtC;AAAA,IACF;AACAC,kBAAE,KAAC,MAAM,KAAK;AAAA,MACZ,KAAK;AAAA,MACL,WAAW;AAAA,KACZ;AAED,UAAM,eAAeA,cAAAA,KAAG,eAAe,QAAQ;AAC/C,QAAI,CAAC,cAAc;AACjBA,oBAAE,KAAC,MAAM,aAAa;AAAA,QACpB,MAAM;AAAA,QACN,SAAS,SAAO;AACdA,wBAAAA,KAAG,eAAe,UAAU,IAAI,OAAO,MAAM;AAAA,QAC9C;AAAA,QACD,MAAM,SAAO;AACXD,wBAAAA,MAAA,MAAA,SAAA,iBAAc,cAAc,GAAG;AAAA,QACjC;AAAA,OACD;AAAA,WACI;AACLA,oBAAAA,MAAY,MAAA,OAAA,iBAAA,cAAc,YAAY;AAAA,IACxC;AAAA,EACD;AAAA,EACD,QAAQ,WAAW;AACjBA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,EACvB;AAAA,EACD,QAAQ,WAAW;AACjBA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,EACxB;AACF;ACnBO,SAAS,YAAY;AAC1B,QAAM,MAAME,cAAY,aAACC,SAAG;AAC5B,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}