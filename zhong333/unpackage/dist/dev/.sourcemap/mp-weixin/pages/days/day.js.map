{"version": 3, "file": "day.js", "sources": ["pages/days/day.vue", "pages/days/day.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"title\">{{ props.date || '今日' }} 的记账</view>\n\n    <view\n      v-for=\"(record, index) in records\"\n      :key=\"record._id\"\n      class=\"record-group\"\n    >\n      <view>\n        <view class=\"record-title\"> {{ record.name }} 的记录：</view>\n        <view\n          v-for=\"(amount, category) in parsedDescriptions[index]\"\n          :key=\"category\"\n          class=\"record-item\"\n        >\n          <template v-if=\"editingKeys[index]?.[category]\">\n            <view class=\"input-container\">\n              <input\n                class=\"input\"\n                v-model=\"editingNames[index][category]\"\n                placeholder=\"项目名称\"\n                @focus=\"showEditOptions(index, category)\"\n                @blur=\"hideEditOptions(index, category)\"\n              />\n              <view class=\"options-dropdown\" v-if=\"showOptions[index]?.[category]\">\n                <view\n                  v-for=\"option in categoryOptions\"\n                  :key=\"option\"\n                  class=\"option-item\"\n                  @click=\"selectOption(index, category, option)\"\n                >\n                  {{ option }}\n                </view>\n              </view>\n            </view>\n          </template>\n          <template v-else>\n            <text class=\"record-text\">{{ category }}</text>\n          </template>\n          <template v-if=\"editingKeys[index]?.[category]\">\n            <input\n              class=\"input\"\n              type=\"digit\"\n              v-model=\"parsedDescriptions[index][category]\"\n              :placeholder=\"category\"\n            />\n            <text @click=\"toggleEdit(index, category)\">完成</text>\n          </template>\n          <template v-else>\n            <text class=\"record-text\">¥{{ parsedDescriptions[index][category] }}</text>\n            <text @click=\"toggleEdit(index, category)\">编辑</text>\n          </template>\n        </view>\n\n        <view class=\"record-item\" v-if=\"!addingKey[index]\">\n          <view class=\"add-item-button\" @click=\"startAdd(index)\">+ 新增</view>\n        </view>\n        <view class=\"record-item\" v-else>\n          <view class=\"input-container\">\n            <input\n              class=\"input\"\n              v-model=\"newItemKey[index]\"\n              placeholder=\"新项目名称\"\n              @focus=\"showNewItemOptions[index] = true\"\n              @blur=\"hideNewItemOptions(index)\"\n            />\n            <view class=\"options-dropdown\" v-if=\"showNewItemOptions[index]\">\n              <view\n                v-for=\"option in categoryOptions\"\n                :key=\"option\"\n                class=\"option-item\"\n                @click=\"selectNewItemOption(index, option)\"\n              >\n                {{ option }}\n              </view>\n            </view>\n          </view>\n          <input class=\"input\" type=\"digit\" v-model=\"newItemValue[index]\" placeholder=\"金额\" />\n          <text class=\"add-button\" @click=\"confirmAdd(index)\">确认</text>\n        </view>\n\n        <view class=\"record-item\">\n          <text class=\"record-text\">总计</text>\n          <text class=\"record-text\">¥{{ getTotal(index) }}</text>\n        </view>\n\n      </view>\n    </view>\n    <view class=\"total-summary\">\n      总金额：¥{{ overallTotal }}\n    </view>\n\n    <view class=\"button-group\">\n      <view class=\"button add-user-button\" @click=\"addNewUser\">+ 新增用户</view>\n      <view class=\"button save-button\" @click=\"saveAll\">保存</view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed } from \"vue\";\n\nconst props = defineProps(['date'])\n\n// 记账记录数据\nconst records = ref([\n  // 示例数据结构（实际数据从云函数加载）\n  // {\n  //   _id: \"zhong333-2025-06-27\",\n  //   name: \"zhong333\",\n  //   date: \"2025-06-27\",\n  //   description: '{\"面包\":105.29,\"外卖\":45,\"水果\":20}',\n  //   createTime: \"2025-06-27T10:00:00.000Z\",\n  //   updateTime: \"2025-06-27T10:00:00.000Z\"\n  // },\n  // {\n  //   _id: \"YPP-2025-06-27\",\n  //   name: \"YPP\",\n  //   date: \"2025-06-27\",\n  //   description: '{\"面包\":15,\"外卖\":45,\"水果\":20}',\n  //   createTime: \"2025-06-27T10:00:00.000Z\",\n  //   updateTime: \"2025-06-27T10:00:00.000Z\"\n  // }\n]);\n\n// 获取指定月份列表\nasync function loadRecords(date) {\n  console.log('加载数据:', date);\n  \n  try {\n    const res = await wx.cloud.callFunction({\n      name: 'getRecordsByDate',\n      data: { date }\n    })\n    if (res.result.success) {\n      const rawData = res.result.data\n      console.log('查询成功:', rawData);\n      records.value = rawData\n\n      // 数据更新后，初始化相关状态\n      initializeEditingStates();\n\n      console.log('parsedDescriptions:', records.value, parsedDescriptions.value);\n\n    } else {\n      console.log('查询失败:', res.result)\n      uni.showToast({ title: '查询失败', icon: 'none' })\n    }\n  } catch (error) {\n    console.log('调用失败:', error);\n    \n    uni.showToast({ title: '调用失败', icon: 'none' })\n  }\n}\n// 初始化页面数据\nif (props.date) {\n  loadRecords(props.date);\n} else {\n  // 如果没有传入日期，使用当前日期\n  const today = new Date().toISOString().split('T')[0];\n  loadRecords(today);\n}\n\n// 解析后的描述数据，将JSON字符串转换为对象\nconst parsedDescriptions = computed(() => {\n  return records.value.map((r) => {\n    try {\n      return JSON.parse(r.description || '{}');\n    } catch {\n      return {};\n    }\n  });\n});\n\n// 编辑时的项目名称映射\nconst editingNames = ref([]);\n\n// 新增项目的名称输入\nconst newItemKey = ref([]);\n// 新增项目的金额输入\nconst newItemValue = ref([]);\n\n// 编辑状态标记，记录哪些项目正在编辑\nconst editingKeys = ref([]);\n\n/**\n * 初始化编辑相关的状态数组\n */\nconst initializeEditingStates = () => {\n  const recordCount = records.value.length;\n\n  // 初始化编辑名称映射\n  editingNames.value = parsedDescriptions.value.map(desc =>\n    Object.fromEntries(Object.keys(desc).map(k => [k, k]))\n  );\n\n  // 重置其他状态数组\n  newItemKey.value = Array(recordCount).fill(\"\");\n  newItemValue.value = Array(recordCount).fill(0);\n  editingKeys.value = Array(recordCount).fill({});\n  addingKey.value = Array(recordCount).fill(false);\n  showOptions.value = Array(recordCount).fill({});\n  showNewItemOptions.value = Array(recordCount).fill(false);\n\n  console.log('编辑状态初始化完成，记录数量:', recordCount);\n};\n\n/**\n * 切换编辑状态或保存编辑内容\n * @param {number} index - 记录的索引\n * @param {string} key - 要编辑的项目名称\n */\nconst toggleEdit = (index, key) => {\n  const newKey = editingNames.value[index][key];\n  const current = parsedDescriptions.value[index][key];\n\n  // 如果当前处于编辑状态，则保存编辑内容\n  if (editingKeys.value[index]?.[key]) {\n    // 如果金额为0，删除该项目\n    if (Number(current) === 0) {\n      delete parsedDescriptions.value[index][key];\n      delete editingNames.value[index][key];\n    } else if (newKey !== key && newKey.trim()) {\n      // 如果项目名称发生变化，更新项目名称\n      parsedDescriptions.value[index][newKey] = current;\n      delete parsedDescriptions.value[index][key];\n      editingNames.value[index][newKey] = newKey;\n      delete editingNames.value[index][key];\n      delete editingKeys.value[index][key];\n      return;\n    }\n  }\n\n  // 切换编辑状态\n  editingKeys.value[index] = {\n    ...editingKeys.value[index],\n    [key]: !editingKeys.value[index]?.[key],\n  };\n\n  // 如果退出编辑状态，隐藏选项下拉框\n  if (!editingKeys.value[index]?.[key]) {\n    showOptions.value[index] = {\n      ...showOptions.value[index],\n      [key]: false\n    };\n  }\n};\n\n// 添加新项目的状态标记\nconst addingKey = ref([]);\n\n// 预设的项目名称选项\nconst categoryOptions = ref([\n  '地铁', '早饭', '午饭', '晚饭'\n]);\n\n// 控制选项下拉框显示状态\nconst showOptions = ref([]);\n\n// 控制新增项目选项下拉框显示状态\nconst showNewItemOptions = ref([]);\n\n/**\n * 显示编辑选项下拉框\n * @param {number} index - 记录的索引\n * @param {string} category - 当前编辑的项目名称\n */\nconst showEditOptions = (index, category) => {\n  showOptions.value[index] = {\n    ...showOptions.value[index],\n    [category]: true\n  };\n};\n\n/**\n * 隐藏编辑选项下拉框（延迟执行以允许点击选项）\n * @param {number} index - 记录的索引\n * @param {string} category - 当前编辑的项目名称\n */\nconst hideEditOptions = (index, category) => {\n  setTimeout(() => {\n    showOptions.value[index] = {\n      ...showOptions.value[index],\n      [category]: false\n    };\n  }, 200);\n};\n\n/**\n * 选择预设选项\n * @param {number} index - 记录的索引\n * @param {string} category - 当前编辑的项目名称\n * @param {string} option - 选择的选项\n */\nconst selectOption = (index, category, option) => {\n  editingNames.value[index][category] = option;\n  showOptions.value[index] = {\n    ...showOptions.value[index],\n    [category]: false\n  };\n};\n\n/**\n * 选择新增项目的预设选项\n * @param {number} index - 记录的索引\n * @param {string} option - 选择的选项\n */\nconst selectNewItemOption = (index, option) => {\n  newItemKey.value[index] = option;\n  showNewItemOptions.value[index] = false;\n};\n\n/**\n * 隐藏新增项目选项下拉框（延迟执行以允许点击选项）\n * @param {number} index - 记录的索引\n */\nconst hideNewItemOptions = (index) => {\n  setTimeout(() => {\n    showNewItemOptions.value[index] = false;\n  }, 200);\n};\n\n/**\n * 开始添加新项目\n * @param {number} index - 记录的索引\n */\nconst startAdd = (index) => {\n  addingKey.value[index] = true;\n};\n\n/**\n * 确认添加新项目\n * @param {number} index - 记录的索引\n */\nconst confirmAdd = (index) => {\n  const key = newItemKey.value[index]?.trim();\n  const value = newItemValue.value[index];\n\n  // 验证项目名称不能为空\n  if (!key) {\n    uni.showToast({ title: \"项目名称不能为空\", icon: \"none\" });\n    return;\n  }\n\n  // 验证金额必须是数字\n  if (isNaN(value)) {\n    uni.showToast({ title: \"金额必须是数字\", icon: \"none\" });\n    return;\n  }\n\n  // 添加新项目到记录中\n  parsedDescriptions.value[index][key] = value;\n  // 重置输入框\n  newItemKey.value[index] = \"\";\n  newItemValue.value[index] = 0;\n  // 退出添加状态\n  addingKey.value[index] = false;\n};\n\n/**\n * 添加新项目（备用方法，当前未使用）\n * @param {number} index - 记录的索引\n */\nconst addItem = (index) => {\n  const key = newItemKey.value[index]?.trim();\n  const value = newItemValue.value[index];\n\n  // 验证项目名称和金额有效性\n  if (key && !isNaN(value)) {\n    parsedDescriptions.value[index][key] = value;\n    newItemKey.value[index] = \"\";\n    newItemValue.value[index] = 0;\n  }\n};\n\n/**\n * 保存所有记录到云端\n * 将解析后的描述数据重新序列化为JSON字符串并保存到云数据库\n */\nconst saveAll = async () => {\n  try {\n    // 显示保存提示\n    uni.showLoading({ title: '保存中...' });\n\n    // 准备要保存的数据\n    const updatedRecords = records.value.map((record, idx) => ({\n      ...record,\n      description: JSON.stringify(parsedDescriptions.value[idx]),\n      updateTime: new Date().toISOString()\n    }));\n\n    console.log(\"准备保存的数据：\", updatedRecords);\n    console.log(\"第一条记录详情：\", JSON.stringify(updatedRecords[0], null, 2));\n\n    // 调用云函数保存数据\n    const res = await wx.cloud.callFunction({\n      name: 'postRecordsByDate',\n      data: {\n        records: updatedRecords,\n        date: props.date\n      }\n    });\n\n    // 隐藏加载提示\n    uni.hideLoading();\n\n    if (res.result && res.result.success) {\n      // 更新本地数据\n      records.value = updatedRecords;\n\n      uni.showToast({\n        title: '保存成功',\n        icon: 'success',\n        duration: 2000\n      });\n\n      console.log(\"保存成功：\", res.result);\n\n    } else {\n      console.log('保存失败:', res.result);\n      uni.showToast({\n        title: res.result?.message || '保存失败',\n        icon: 'none'\n      });\n    }\n\n  } catch (error) {\n    console.log('保存出错:', error);\n    uni.hideLoading();\n    uni.showToast({\n      title: '网络错误，请重试',\n      icon: 'none'\n    });\n  }\n};\n\n/**\n * 新增用户记录\n * 为当前日期添加一个新的用户记录\n */\nconst addNewUser = () => {\n  // 预设的用户列表\n  const userOptions = [\n    { name: 'zhong333' },\n    { name: 'YPP' }\n  ];\n\n  // 检查当前已有的用户名\n  const existingNames = records.value.map(record => record.name);\n  const availableUsers = userOptions.filter(user => !existingNames.includes(user.name));\n\n  if (availableUsers.length === 0) {\n    uni.showToast({\n      title: '所有用户都已添加',\n      icon: 'none',\n      duration: 2000\n    });\n    return;\n  }\n\n  // 显示用户选择弹窗\n  const userNames = availableUsers.map(user => user.name);\n\n  uni.showActionSheet({\n    itemList: userNames,\n    success: (res) => {\n      const selectedUser = availableUsers[res.tapIndex];\n\n      // 创建新的记录\n      const newRecord = {\n        _id: `${selectedUser.name}-${props.date}`,\n        name: selectedUser.name,\n        date: props.date || new Date().toISOString().split('T')[0],\n        description: '{}', // 空的记录\n        createTime: new Date().toISOString(),\n        updateTime: new Date().toISOString()\n      };\n\n      // 添加到records数组\n      records.value.push(newRecord);\n\n      // 重新初始化编辑状态\n      initializeEditingStates();\n\n      console.log('新增用户记录:', newRecord);\n\n      uni.showToast({\n        title: `新增${selectedUser.name}成功`,\n        icon: 'none',\n        duration: 2000\n      });\n    },\n    fail: () => {\n      console.log('用户取消选择');\n    }\n  });\n};\n\n/**\n * 计算单个记录的总金额\n * @param {number} index - 记录的索引\n * @returns {string} 格式化后的总金额（保留两位小数）\n */\nconst getTotal = (index) => {\n  const desc = parsedDescriptions?.value[index];\n  return parsedDescriptions.value.length > 0 ? Object.values(desc).reduce((sum, v) => sum + Number(v || 0), 0).toFixed(2) : 0;\n};\n\n/**\n * 处理数字输入，确保支持小数点\n * @param {Event} e - 输入事件\n */\nconst handleNumberInput = (e) => {\n  const value = e.detail.value;\n  // 允许数字和小数点，移除其他字符\n  const cleanValue = value.replace(/[^\\d.]/g, '');\n  // 确保只有一个小数点\n  const parts = cleanValue.split('.');\n  if (parts.length > 2) {\n    return parts[0] + '.' + parts.slice(1).join('');\n  }\n  return cleanValue;\n};\n\n/**\n * 计算所有记录的总金额（计算属性）\n * @returns {string} 格式化后的总金额（保留两位小数）\n */\nconst overallTotal = computed(() => {\n  return parsedDescriptions.value.reduce((sum, desc) => {\n    return sum + Object.values(desc).reduce((s, v) => s + Number(v || 0), 0);\n  }, 0).toFixed(2);\n});\n</script>\n\n<style>\n/* 全局样式 */\npage {\n  background-color: #f8f9fa;\n}\n\n.container {\n  padding: 32rpx 24rpx;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n/* 标题样式 */\n.title {\n  font-size: 36rpx;\n  font-weight: 500;\n  margin-bottom: 32rpx;\n  text-align: center;\n  color: #2c3e50;\n}\n\n/* 输入框样式 */\n.input {\n  width: 180rpx;\n  height: 56rpx;\n  border: 1rpx solid #ddd;\n  border-radius: 8rpx;\n  padding: 0 12rpx;\n  font-size: 28rpx;\n  background-color: #ffffff;\n  box-sizing: border-box;\n}\n\n.input:focus {\n  border-color: #007aff;\n  outline: none;\n}\n\n/* 操作按钮样式 */\n.add-button {\n  font-size: 28rpx;\n  color: #007aff;\n  padding: 8rpx 12rpx;\n  border-radius: 6rpx;\n}\n/* 按钮组样式 */\n.button-group {\n  display: flex;\n  gap: 16rpx;\n  margin: 32rpx 0;\n}\n\n.button {\n  flex: 1;\n  padding: 24rpx 20rpx;\n  border-radius: 8rpx;\n  font-size: 30rpx;\n  text-align: center;\n  color: #ffffff;\n  border: none;\n}\n\n/* 保存按钮样式 */\n.save-button {\n  background-color: #007aff;\n}\n\n/* 新增用户按钮样式 */\n.add-user-button {\n  background-color: #28a745;\n}\n/* 记录组样式 */\n.record-group {\n  margin-bottom: 24rpx;\n  padding: 24rpx;\n  background-color: #ffffff;\n  border-radius: 12rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n  border: 1rpx solid #e9ecef;\n}\n\n.record-title {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #495057;\n  margin-bottom: 16rpx;\n  padding-bottom: 12rpx;\n  border-bottom: 1rpx solid #e9ecef;\n}\n\n.record-item {\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f8f9fa;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.record-item:last-child {\n  border-bottom: none;\n}\n\n.record-text {\n  font-size: 28rpx;\n  width: 140rpx;\n  text-align: left;\n  color: #495057;\n}\n/* 新增项目按钮样式 */\n.add-item-button {\n  color: #007aff;\n  font-size: 28rpx;\n  text-align: center;\n  padding: 20rpx 0;\n  width: 100%;\n  background-color: #f8f9fa;\n  border: 1rpx dashed #007aff;\n  border-radius: 8rpx;\n}\n\n/* 总计样式 */\n.total-summary {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #2c3e50;\n  text-align: center;\n  margin: 32rpx 0;\n  padding: 24rpx;\n  background-color: #ffffff;\n  border-radius: 12rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n  border: 1rpx solid #e9ecef;\n}\n\n/* 输入容器样式 */\n.input-container {\n  position: relative;\n  display: inline-block;\n}\n\n/* 选项下拉框样式 */\n.options-dropdown {\n  position: absolute;\n  top: calc(100% + 4rpx);\n  left: 0;\n  right: 0;\n  background-color: #ffffff;\n  border: 1rpx solid #ddd;\n  border-radius: 8rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  max-height: 300rpx;\n  overflow-y: auto;\n}\n\n/* 选项项样式 */\n.option-item {\n  padding: 16rpx;\n  font-size: 28rpx;\n  color: #495057;\n  border-bottom: 1rpx solid #f8f9fa;\n}\n\n.option-item:hover {\n  background-color: #f8f9fa;\n  color: #007aff;\n}\n\n.option-item:last-child {\n  border-bottom: none;\n}\n</style>", "import MiniProgramPage from '/Users/<USER>/Projects/zhong333/zhong333/pages/days/day.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "wx", "computed", "MiniProgramPage"], "mappings": ";;;;;;AAuGA,UAAM,QAAQ;AAGd,UAAM,UAAUA,cAAAA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAkBpB,CAAC;AAGD,mBAAe,YAAY,MAAM;AAC/BC,oBAAY,MAAA,MAAA,OAAA,6BAAA,SAAS,IAAI;AAEzB,UAAI;AACF,cAAM,MAAM,MAAMC,mBAAG,MAAM,aAAa;AAAA,UACtC,MAAM;AAAA,UACN,MAAM,EAAE,KAAM;AAAA,QACpB,CAAK;AACD,YAAI,IAAI,OAAO,SAAS;AACtB,gBAAM,UAAU,IAAI,OAAO;AAC3BD,wBAAY,MAAA,MAAA,OAAA,6BAAA,SAAS,OAAO;AAC5B,kBAAQ,QAAQ;AAGhB;AAEAA,8BAAY,MAAA,OAAA,6BAAA,uBAAuB,QAAQ,OAAO,mBAAmB,KAAK;AAAA,QAEhF,OAAW;AACLA,wEAAY,SAAS,IAAI,MAAM;AAC/BA,wBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,QAC9C;AAAA,MACF,SAAQ,OAAO;AACdA,sEAAY,SAAS,KAAK;AAE1BA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,MAC9C;AAAA,IACH;AAEA,QAAI,MAAM,MAAM;AACd,kBAAY,MAAM,IAAI;AAAA,IACxB,OAAO;AAEL,YAAM,SAAQ,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AACnD,kBAAY,KAAK;AAAA,IACnB;AAGA,UAAM,qBAAqBE,cAAQ,SAAC,MAAM;AACxC,aAAO,QAAQ,MAAM,IAAI,CAAC,MAAM;AAC9B,YAAI;AACF,iBAAO,KAAK,MAAM,EAAE,eAAe,IAAI;AAAA,QAC7C,QAAY;AACN,iBAAO;QACR;AAAA,MACL,CAAG;AAAA,IACH,CAAC;AAGD,UAAM,eAAeH,cAAAA,IAAI,CAAA,CAAE;AAG3B,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AAEzB,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAG3B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAK1B,UAAM,0BAA0B,MAAM;AACpC,YAAM,cAAc,QAAQ,MAAM;AAGlC,mBAAa,QAAQ,mBAAmB,MAAM;AAAA,QAAI,UAChD,OAAO,YAAY,OAAO,KAAK,IAAI,EAAE,IAAI,OAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA,MACzD;AAGE,iBAAW,QAAQ,MAAM,WAAW,EAAE,KAAK,EAAE;AAC7C,mBAAa,QAAQ,MAAM,WAAW,EAAE,KAAK,CAAC;AAC9C,kBAAY,QAAQ,MAAM,WAAW,EAAE,KAAK,CAAA,CAAE;AAC9C,gBAAU,QAAQ,MAAM,WAAW,EAAE,KAAK,KAAK;AAC/C,kBAAY,QAAQ,MAAM,WAAW,EAAE,KAAK,CAAA,CAAE;AAC9C,yBAAmB,QAAQ,MAAM,WAAW,EAAE,KAAK,KAAK;AAExDC,oBAAA,MAAA,MAAA,OAAA,6BAAY,mBAAmB,WAAW;AAAA,IAC5C;AAOA,UAAM,aAAa,CAAC,OAAO,QAAQ;;AACjC,YAAM,SAAS,aAAa,MAAM,KAAK,EAAE,GAAG;AAC5C,YAAM,UAAU,mBAAmB,MAAM,KAAK,EAAE,GAAG;AAGnD,WAAI,iBAAY,MAAM,KAAK,MAAvB,mBAA2B,MAAM;AAEnC,YAAI,OAAO,OAAO,MAAM,GAAG;AACzB,iBAAO,mBAAmB,MAAM,KAAK,EAAE,GAAG;AAC1C,iBAAO,aAAa,MAAM,KAAK,EAAE,GAAG;AAAA,QACrC,WAAU,WAAW,OAAO,OAAO,KAAI,GAAI;AAE1C,6BAAmB,MAAM,KAAK,EAAE,MAAM,IAAI;AAC1C,iBAAO,mBAAmB,MAAM,KAAK,EAAE,GAAG;AAC1C,uBAAa,MAAM,KAAK,EAAE,MAAM,IAAI;AACpC,iBAAO,aAAa,MAAM,KAAK,EAAE,GAAG;AACpC,iBAAO,YAAY,MAAM,KAAK,EAAE,GAAG;AACnC;AAAA,QACD;AAAA,MACF;AAGD,kBAAY,MAAM,KAAK,IAAI;AAAA,QACzB,GAAG,YAAY,MAAM,KAAK;AAAA,QAC1B,CAAC,GAAG,GAAG,GAAC,iBAAY,MAAM,KAAK,MAAvB,mBAA2B;AAAA,MACvC;AAGE,UAAI,GAAC,iBAAY,MAAM,KAAK,MAAvB,mBAA2B,OAAM;AACpC,oBAAY,MAAM,KAAK,IAAI;AAAA,UACzB,GAAG,YAAY,MAAM,KAAK;AAAA,UAC1B,CAAC,GAAG,GAAG;AAAA,QACb;AAAA,MACG;AAAA,IACH;AAGA,UAAM,YAAYD,cAAAA,IAAI,CAAA,CAAE;AAGxB,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,IACpB,CAAC;AAGD,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAG1B,UAAM,qBAAqBA,cAAAA,IAAI,CAAA,CAAE;AAOjC,UAAM,kBAAkB,CAAC,OAAO,aAAa;AAC3C,kBAAY,MAAM,KAAK,IAAI;AAAA,QACzB,GAAG,YAAY,MAAM,KAAK;AAAA,QAC1B,CAAC,QAAQ,GAAG;AAAA,MAChB;AAAA,IACA;AAOA,UAAM,kBAAkB,CAAC,OAAO,aAAa;AAC3C,iBAAW,MAAM;AACf,oBAAY,MAAM,KAAK,IAAI;AAAA,UACzB,GAAG,YAAY,MAAM,KAAK;AAAA,UAC1B,CAAC,QAAQ,GAAG;AAAA,QAClB;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAQA,UAAM,eAAe,CAAC,OAAO,UAAU,WAAW;AAChD,mBAAa,MAAM,KAAK,EAAE,QAAQ,IAAI;AACtC,kBAAY,MAAM,KAAK,IAAI;AAAA,QACzB,GAAG,YAAY,MAAM,KAAK;AAAA,QAC1B,CAAC,QAAQ,GAAG;AAAA,MAChB;AAAA,IACA;AAOA,UAAM,sBAAsB,CAAC,OAAO,WAAW;AAC7C,iBAAW,MAAM,KAAK,IAAI;AAC1B,yBAAmB,MAAM,KAAK,IAAI;AAAA,IACpC;AAMA,UAAM,qBAAqB,CAAC,UAAU;AACpC,iBAAW,MAAM;AACf,2BAAmB,MAAM,KAAK,IAAI;AAAA,MACnC,GAAE,GAAG;AAAA,IACR;AAMA,UAAM,WAAW,CAAC,UAAU;AAC1B,gBAAU,MAAM,KAAK,IAAI;AAAA,IAC3B;AAMA,UAAM,aAAa,CAAC,UAAU;;AAC5B,YAAM,OAAM,gBAAW,MAAM,KAAK,MAAtB,mBAAyB;AACrC,YAAM,QAAQ,aAAa,MAAM,KAAK;AAGtC,UAAI,CAAC,KAAK;AACRC,sBAAG,MAAC,UAAU,EAAE,OAAO,YAAY,MAAM,OAAM,CAAE;AACjD;AAAA,MACD;AAGD,UAAI,MAAM,KAAK,GAAG;AAChBA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAChD;AAAA,MACD;AAGD,yBAAmB,MAAM,KAAK,EAAE,GAAG,IAAI;AAEvC,iBAAW,MAAM,KAAK,IAAI;AAC1B,mBAAa,MAAM,KAAK,IAAI;AAE5B,gBAAU,MAAM,KAAK,IAAI;AAAA,IAC3B;AAsBA,UAAM,UAAU,YAAY;;AAC1B,UAAI;AAEFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAGnC,cAAM,iBAAiB,QAAQ,MAAM,IAAI,CAAC,QAAQ,SAAS;AAAA,UACzD,GAAG;AAAA,UACH,aAAa,KAAK,UAAU,mBAAmB,MAAM,GAAG,CAAC;AAAA,UACzD,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,QACrC,EAAC;AAEFA,sBAAA,MAAA,MAAA,OAAA,6BAAY,YAAY,cAAc;AACtCA,sBAAAA,MAAY,MAAA,OAAA,6BAAA,YAAY,KAAK,UAAU,eAAe,CAAC,GAAG,MAAM,CAAC,CAAC;AAGlE,cAAM,MAAM,MAAMC,mBAAG,MAAM,aAAa;AAAA,UACtC,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,SAAS;AAAA,YACT,MAAM,MAAM;AAAA,UACb;AAAA,QACP,CAAK;AAGDD,sBAAG,MAAC,YAAW;AAEf,YAAI,IAAI,UAAU,IAAI,OAAO,SAAS;AAEpC,kBAAQ,QAAQ;AAEhBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAEDA,wBAAY,MAAA,MAAA,OAAA,6BAAA,SAAS,IAAI,MAAM;AAAA,QAErC,OAAW;AACLA,wBAAY,MAAA,MAAA,OAAA,6BAAA,SAAS,IAAI,MAAM;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,SAAO,SAAI,WAAJ,mBAAY,YAAW;AAAA,YAC9B,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MAEF,SAAQ,OAAO;AACdA,sEAAY,SAAS,KAAK;AAC1BA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAMA,UAAM,aAAa,MAAM;AAEvB,YAAM,cAAc;AAAA,QAClB,EAAE,MAAM,WAAY;AAAA,QACpB,EAAE,MAAM,MAAO;AAAA,MACnB;AAGE,YAAM,gBAAgB,QAAQ,MAAM,IAAI,YAAU,OAAO,IAAI;AAC7D,YAAM,iBAAiB,YAAY,OAAO,UAAQ,CAAC,cAAc,SAAS,KAAK,IAAI,CAAC;AAEpF,UAAI,eAAe,WAAW,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AAGD,YAAM,YAAY,eAAe,IAAI,UAAQ,KAAK,IAAI;AAEtDA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AAChB,gBAAM,eAAe,eAAe,IAAI,QAAQ;AAGhD,gBAAM,YAAY;AAAA,YAChB,KAAK,GAAG,aAAa,IAAI,IAAI,MAAM,IAAI;AAAA,YACvC,MAAM,aAAa;AAAA,YACnB,MAAM,MAAM,SAAQ,oBAAI,KAAI,GAAG,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC;AAAA,YACzD,aAAa;AAAA;AAAA,YACb,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,YACpC,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,UAC5C;AAGM,kBAAQ,MAAM,KAAK,SAAS;AAG5B;AAEAA,wBAAY,MAAA,MAAA,OAAA,6BAAA,WAAW,SAAS;AAEhCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,KAAK,aAAa,IAAI;AAAA,YAC7B,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAY,MAAA,OAAA,6BAAA,QAAQ;AAAA,QACrB;AAAA,MACL,CAAG;AAAA,IACH;AAOA,UAAM,WAAW,CAAC,UAAU;AAC1B,YAAM,OAAO,yDAAoB,MAAM;AACvC,aAAO,mBAAmB,MAAM,SAAS,IAAI,OAAO,OAAO,IAAI,EAAE,OAAO,CAAC,KAAK,MAAM,MAAM,OAAO,KAAK,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,IAAI;AAAA,IAC5H;AAsBA,UAAM,eAAeE,cAAQ,SAAC,MAAM;AAClC,aAAO,mBAAmB,MAAM,OAAO,CAAC,KAAK,SAAS;AACpD,eAAO,MAAM,OAAO,OAAO,IAAI,EAAE,OAAO,CAAC,GAAG,MAAM,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC;AAAA,MACxE,GAAE,CAAC,EAAE,QAAQ,CAAC;AAAA,IACjB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACphBD,GAAG,WAAWC,SAAe;"}