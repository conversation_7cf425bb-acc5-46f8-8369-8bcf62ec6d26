{"version": 3, "file": "month.js", "sources": ["pages/month/month.vue", "pages/month/month.vue?type=page"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"title\">{{ monthTitle }} 月度详情</view>\n    \n    <!-- 月度统计概览 -->\n    <view class=\"month-overview\">\n      <view class=\"overview-item\">\n        <text class=\"overview-label\">总金额</text>\n        <text class=\"overview-value\">¥{{ monthTotal }}</text>\n      </view>\n      <view class=\"overview-item\">\n        <text class=\"overview-label\">记录数</text>\n        <text class=\"overview-value\">{{ totalRecords }}条</text>\n      </view>\n      <view class=\"overview-item\">\n        <text class=\"overview-label\">记账天数</text>\n        <text class=\"overview-value\">{{ activeDays }}天</text>\n      </view>\n    </view>\n\n    <!-- 按人员分组显示 -->\n    <view class=\"user-groups\">\n      <view \n        v-for=\"(userGroup, index) in userGroups\" \n        :key=\"userGroup.name\"\n        class=\"user-group\"\n      >\n        <view class=\"user-header\">\n          <text class=\"user-name\">{{ userGroup.name }}</text>\n          <text class=\"user-total\">¥{{ userGroup.total }}</text>\n        </view>\n        \n        <!-- 该用户的分类汇总 -->\n        <view class=\"user-categories\">\n          <view\n            v-for=\"(categoryData, categoryName) in userGroup.categories\"\n            :key=\"categoryName\"\n            class=\"category-item\"\n          >\n            <view class=\"category-info\">\n              <text class=\"category-name\">{{ categoryName }}</text>\n              <text class=\"category-count\">{{ categoryData.count }}次</text>\n            </view>\n            <text class=\"category-total\">¥{{ categoryData.total }}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 空状态 -->\n    <view v-if=\"userGroups.length === 0\" class=\"empty-state\">\n      <text class=\"empty-text\">该月暂无记录</text>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\n\n// 响应式数据\nconst records = ref([])\nconst userGroups = ref([])\nconst currentMonth = ref('')\n\n// 计算属性\nconst monthTitle = computed(() => {\n  if (!currentMonth.value) return ''\n  const [year, monthNum] = currentMonth.value.split('-')\n  return `${year}年${parseInt(monthNum)}月`\n})\n\nconst monthTotal = computed(() => {\n  return userGroups.value.reduce((sum, group) => {\n    return sum + parseFloat(group.total || 0)\n  }, 0).toFixed(2)\n})\n\nconst totalRecords = computed(() => {\n  return records.value.length\n})\n\nconst activeDays = computed(() => {\n  const days = new Set()\n  records.value.forEach(record => {\n    if (record.date) {\n      days.add(record.date)\n    }\n  })\n  return days.size\n})\n\n\n\n\n\n/**\n * 加载月度数据\n */\nconst loadMonthData = async () => {\n  try {\n    console.log('加载月度数据:', currentMonth.value)\n\n    uni.showLoading({ title: '加载中...' })\n\n    const res = await wx.cloud.callFunction({\n      name: 'getRecordsByDate',\n      data: { date: currentMonth.value }\n    })\n    \n    uni.hideLoading()\n    \n    if (res.result.success) {\n      records.value = res.result.data || []\n      console.log('月度记录:', records.value)\n      \n      // 按用户分组\n      groupRecordsByUser()\n    } else {\n      console.log('查询失败:', res.result)\n      uni.showToast({ \n        title: res.result.error || '查询失败', \n        icon: 'none' \n      })\n    }\n  } catch (error) {\n    console.log('调用失败:', error)\n    uni.hideLoading()\n    uni.showToast({ \n      title: '网络错误，请重试', \n      icon: 'none' \n    })\n  }\n}\n\n/**\n * 按用户分组记录并按类型汇总\n */\nconst groupRecordsByUser = () => {\n  const groups = {}\n\n  records.value.forEach(record => {\n    const userName = record.name || '未知用户'\n\n    if (!groups[userName]) {\n      groups[userName] = {\n        name: userName,\n        records: [],\n        categories: {}, // 按类型汇总\n        total: 0\n      }\n    }\n\n    groups[userName].records.push(record)\n\n    // 解析记录描述，按类型汇总\n    try {\n      const description = JSON.parse(record.description || '{}')\n      Object.entries(description).forEach(([category, amount]) => {\n        const numAmount = parseFloat(amount) || 0\n\n        if (!groups[userName].categories[category]) {\n          groups[userName].categories[category] = {\n            total: 0,\n            count: 0\n          }\n        }\n\n        groups[userName].categories[category].total += numAmount\n        groups[userName].categories[category].count += 1\n        groups[userName].total += numAmount\n      })\n    } catch (error) {\n      console.error('解析记录描述失败:', error)\n    }\n  })\n\n  // 转换为数组并按总金额排序\n  userGroups.value = Object.values(groups)\n    .map(group => ({\n      ...group,\n      total: group.total.toFixed(2),\n      // 将categories对象转换为数组，按金额排序\n      categories: Object.entries(group.categories)\n        .map(([name, data]) => ({\n          name,\n          total: data.total.toFixed(2),\n          count: data.count\n        }))\n        .sort((a, b) => parseFloat(b.total) - parseFloat(a.total))\n        .reduce((obj, item) => {\n          obj[item.name] = {\n            total: item.total,\n            count: item.count\n          }\n          return obj\n        }, {})\n    }))\n    .sort((a, b) => parseFloat(b.total) - parseFloat(a.total)) // 按金额倒序\n\n  console.log('用户分组和类型汇总:', userGroups.value)\n}\n\n// 页面加载时获取路由参数并加载数据\nonMounted(() => {\n  // 获取路由参数\n  const pages = uni.getCurrentPages()\n  const currentPage = pages[pages.length - 1]\n  const options = currentPage.options\n\n  if (options && options.month) {\n    currentMonth.value = options.month\n    console.log('获取到月份参数:', currentMonth.value)\n    loadMonthData()\n  } else {\n    uni.showToast({\n      title: '缺少月份参数',\n      icon: 'none'\n    })\n  }\n})\n</script>\n\n<style>\n/* 全局样式 */\npage {\n  background-color: #f8f9fa;\n}\n\n.container {\n  padding: 24rpx;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n/* 标题样式 */\n.title {\n  font-size: 36rpx;\n  font-weight: 500;\n  margin-bottom: 32rpx;\n  text-align: center;\n  color: #2c3e50;\n}\n\n/* 月度概览样式 */\n.month-overview {\n  display: flex;\n  justify-content: space-around;\n  background-color: #ffffff;\n  border-radius: 12rpx;\n  padding: 32rpx;\n  margin-bottom: 24rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n}\n\n.overview-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.overview-label {\n  font-size: 26rpx;\n  color: #6c757d;\n  margin-bottom: 8rpx;\n}\n\n.overview-value {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n/* 用户分组样式 */\n.user-groups {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.user-group {\n  background-color: #ffffff;\n  border-radius: 12rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n  overflow: hidden;\n}\n\n/* 用户头部样式 */\n.user-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx;\n  background-color: #007aff;\n  color: #ffffff;\n}\n\n.user-name {\n  font-size: 32rpx;\n  font-weight: 600;\n}\n\n.user-total {\n  font-size: 30rpx;\n  font-weight: 700;\n}\n\n/* 用户分类汇总样式 */\n.user-categories {\n  padding: 16rpx;\n}\n\n.category-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx;\n  margin-bottom: 12rpx;\n  background-color: #f8f9fa;\n  border-radius: 12rpx;\n  border-left: 4rpx solid #28a745;\n  transition: all 0.2s ease;\n}\n\n.category-item:hover {\n  background-color: #e9ecef;\n  transform: translateX(4rpx);\n}\n\n.category-item:last-child {\n  margin-bottom: 0;\n}\n\n/* 分类信息区域 */\n.category-info {\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n}\n\n.category-name {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 4rpx;\n}\n\n.category-count {\n  font-size: 24rpx;\n  color: #6c757d;\n}\n\n/* 分类总金额 */\n.category-total {\n  font-size: 32rpx;\n  font-weight: 700;\n  color: #28a745;\n  background-color: #d4edda;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  border: 1rpx solid #c3e6cb;\n}\n\n/* 空状态样式 */\n.empty-state {\n  text-align: center;\n  padding: 80rpx 0;\n}\n\n.empty-text {\n  font-size: 30rpx;\n  color: #adb5bd;\n}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Projects/zhong333/zhong333/pages/month/month.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "wx", "onMounted", "MiniProgramPage"], "mappings": ";;;;;AA4DA,UAAM,UAAUA,cAAG,IAAC,EAAE;AACtB,UAAM,aAAaA,cAAG,IAAC,EAAE;AACzB,UAAM,eAAeA,cAAG,IAAC,EAAE;AAG3B,UAAM,aAAaC,cAAQ,SAAC,MAAM;AAChC,UAAI,CAAC,aAAa;AAAO,eAAO;AAChC,YAAM,CAAC,MAAM,QAAQ,IAAI,aAAa,MAAM,MAAM,GAAG;AACrD,aAAO,GAAG,IAAI,IAAI,SAAS,QAAQ,CAAC;AAAA,IACtC,CAAC;AAED,UAAM,aAAaA,cAAQ,SAAC,MAAM;AAChC,aAAO,WAAW,MAAM,OAAO,CAAC,KAAK,UAAU;AAC7C,eAAO,MAAM,WAAW,MAAM,SAAS,CAAC;AAAA,MAC5C,GAAK,CAAC,EAAE,QAAQ,CAAC;AAAA,IACjB,CAAC;AAED,UAAM,eAAeA,cAAQ,SAAC,MAAM;AAClC,aAAO,QAAQ,MAAM;AAAA,IACvB,CAAC;AAED,UAAM,aAAaA,cAAQ,SAAC,MAAM;AAChC,YAAM,OAAO,oBAAI,IAAK;AACtB,cAAQ,MAAM,QAAQ,YAAU;AAC9B,YAAI,OAAO,MAAM;AACf,eAAK,IAAI,OAAO,IAAI;AAAA,QACrB;AAAA,MACL,CAAG;AACD,aAAO,KAAK;AAAA,IACd,CAAC;AASD,UAAM,gBAAgB,YAAY;AAChC,UAAI;AACFC,sBAAA,MAAA,MAAA,OAAA,gCAAY,WAAW,aAAa,KAAK;AAEzCA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAQ,CAAE;AAEnC,cAAM,MAAM,MAAMC,mBAAG,MAAM,aAAa;AAAA,UACtC,MAAM;AAAA,UACN,MAAM,EAAE,MAAM,aAAa,MAAO;AAAA,QACxC,CAAK;AAEDD,sBAAAA,MAAI,YAAa;AAEjB,YAAI,IAAI,OAAO,SAAS;AACtB,kBAAQ,QAAQ,IAAI,OAAO,QAAQ,CAAE;AACrCA,wBAAA,MAAA,MAAA,OAAA,gCAAY,SAAS,QAAQ,KAAK;AAGlC,6BAAoB;AAAA,QAC1B,OAAW;AACLA,2EAAY,SAAS,IAAI,MAAM;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO,SAAS;AAAA,YAC3B,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,mDAAY,SAAS,KAAK;AAC1BA,sBAAAA,MAAI,YAAa;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAKA,UAAM,qBAAqB,MAAM;AAC/B,YAAM,SAAS,CAAE;AAEjB,cAAQ,MAAM,QAAQ,YAAU;AAC9B,cAAM,WAAW,OAAO,QAAQ;AAEhC,YAAI,CAAC,OAAO,QAAQ,GAAG;AACrB,iBAAO,QAAQ,IAAI;AAAA,YACjB,MAAM;AAAA,YACN,SAAS,CAAE;AAAA,YACX,YAAY,CAAE;AAAA;AAAA,YACd,OAAO;AAAA,UACR;AAAA,QACF;AAED,eAAO,QAAQ,EAAE,QAAQ,KAAK,MAAM;AAGpC,YAAI;AACF,gBAAM,cAAc,KAAK,MAAM,OAAO,eAAe,IAAI;AACzD,iBAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAAC,UAAU,MAAM,MAAM;AAC1D,kBAAM,YAAY,WAAW,MAAM,KAAK;AAExC,gBAAI,CAAC,OAAO,QAAQ,EAAE,WAAW,QAAQ,GAAG;AAC1C,qBAAO,QAAQ,EAAE,WAAW,QAAQ,IAAI;AAAA,gBACtC,OAAO;AAAA,gBACP,OAAO;AAAA,cACR;AAAA,YACF;AAED,mBAAO,QAAQ,EAAE,WAAW,QAAQ,EAAE,SAAS;AAC/C,mBAAO,QAAQ,EAAE,WAAW,QAAQ,EAAE,SAAS;AAC/C,mBAAO,QAAQ,EAAE,SAAS;AAAA,UAClC,CAAO;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,qDAAc,aAAa,KAAK;AAAA,QACjC;AAAA,MACL,CAAG;AAGD,iBAAW,QAAQ,OAAO,OAAO,MAAM,EACpC,IAAI,YAAU;AAAA,QACb,GAAG;AAAA,QACH,OAAO,MAAM,MAAM,QAAQ,CAAC;AAAA;AAAA,QAE5B,YAAY,OAAO,QAAQ,MAAM,UAAU,EACxC,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO;AAAA,UACtB;AAAA,UACA,OAAO,KAAK,MAAM,QAAQ,CAAC;AAAA,UAC3B,OAAO,KAAK;AAAA,QACtB,EAAU,EACD,KAAK,CAAC,GAAG,MAAM,WAAW,EAAE,KAAK,IAAI,WAAW,EAAE,KAAK,CAAC,EACxD,OAAO,CAAC,KAAK,SAAS;AACrB,cAAI,KAAK,IAAI,IAAI;AAAA,YACf,OAAO,KAAK;AAAA,YACZ,OAAO,KAAK;AAAA,UACb;AACD,iBAAO;AAAA,QACR,GAAE,EAAE;AAAA,MACb,EAAM,EACD,KAAK,CAAC,GAAG,MAAM,WAAW,EAAE,KAAK,IAAI,WAAW,EAAE,KAAK,CAAC;AAE3DA,oBAAA,MAAA,MAAA,OAAA,gCAAY,cAAc,WAAW,KAAK;AAAA,IAC5C;AAGAE,kBAAAA,UAAU,MAAM;AAEd,YAAM,QAAQF,cAAG,MAAC,gBAAiB;AACnC,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY;AAE5B,UAAI,WAAW,QAAQ,OAAO;AAC5B,qBAAa,QAAQ,QAAQ;AAC7BA,sBAAA,MAAA,MAAA,OAAA,gCAAY,YAAY,aAAa,KAAK;AAC1C,sBAAe;AAAA,MACnB,OAAS;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1ND,GAAG,WAAWG,SAAe;"}