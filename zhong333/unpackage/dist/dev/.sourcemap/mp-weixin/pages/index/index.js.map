{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "pages/index/index.vue?type=page"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 视图模式选择 -->\r\n    <view class=\"mode-selector\">\r\n      <view\r\n        class=\"mode-button\"\r\n        :class=\"{ active: viewMode === 'month' }\"\r\n        @click=\"switchViewMode('month')\"\r\n      >\r\n        月视图\r\n      </view>\r\n      <view\r\n        class=\"mode-button\"\r\n        :class=\"{ active: viewMode === 'year' }\"\r\n        @click=\"switchViewMode('year')\"\r\n      >\r\n        年视图\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 日期选择器 -->\r\n    <picker\r\n      mode=\"date\"\r\n      :fields=\"viewMode === 'year' ? 'year' : 'month'\"\r\n      @change=\"onDateChange\"\r\n    >\r\n      <view class=\"date-picker\">\r\n        {{ viewMode === 'year' ? selectedYear + '年' : '选择月份：' + selectedMonth }}\r\n      </view>\r\n    </picker>\r\n    <view class=\"date-list\">\r\n      <view\r\n        class=\"date-item\"\r\n        v-for=\"(item, index) in dateList\"\r\n        :key=\"index\"\r\n        @click=\"handleItemClick(item)\"\r\n        :class=\"{ 'has-records': item.records && item.records.length > 0 }\"\r\n      >\r\n        <view class=\"date-header\">\r\n          <text class=\"date-text\">{{ formatDisplayDate(item.date, item.isMonthSummary) }}</text>\r\n          <view v-if=\"item.records && item.records.length > 0\" class=\"total-badge\">\r\n            ¥{{ item.isMonthSummary ? item.totalAmount : getDayTotal(item.records) }}\r\n          </view>\r\n        </view>\r\n\r\n        <view\r\n          v-if=\"item.records && item.records.length > 0\"\r\n          class=\"record-list\"\r\n        >\r\n          <view\r\n            v-for=\"(record, rIndex) in item.records\"\r\n            :key=\"rIndex\"\r\n            class=\"record-item\"\r\n          >\r\n            <view class=\"record-summary\">\r\n              <text class=\"user-name\">{{ record.name }}</text>\r\n              <text class=\"amount\">¥{{ getTotal(record.description) }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view v-else class=\"no-records\">\r\n          <text class=\"no-records-text\">暂无记录</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { onShow } from '@dcloudio/uni-app'\r\n\r\nconst dateList = ref([])\r\nconst selectedMonth = ref('')\r\nconst selectedYear = ref('')\r\nconst viewMode = ref('month') // 'month' 或 'year'\r\n\r\n// 计算单个记录总数\r\nfunction getTotal(descriptionStr) {\r\n  try {\r\n    const data = JSON.parse(descriptionStr)\r\n    console.log(data);\r\n\r\n    // 确保每个值是数字\r\n    const total = Object.values(data).reduce((sum, val) => {\r\n      const num = parseFloat(val)\r\n      return sum + (isNaN(num) ? 0 : num)\r\n    }, 0)\r\n\r\n    return Number(total.toFixed(2)) // 输出数字类型，保留两位小数\r\n  } catch (e) {\r\n    return 0\r\n  }\r\n}\r\n\r\n// 计算一天所有记录的总数\r\nfunction getDayTotal(records) {\r\n  const total = records.reduce((sum, record) => {\r\n    return sum + getTotal(record.description)\r\n  }, 0)\r\n  return Number(total.toFixed(2))\r\n}\r\n\r\n// 格式化显示日期\r\nfunction formatDisplayDate(dateStr, isMonthSummary = false) {\r\n  // 如果是月度汇总（年视图）\r\n  if (isMonthSummary) {\r\n    const [year, month] = dateStr.split('-')\r\n    const monthNames = [\r\n      '1月', '2月', '3月', '4月', '5月', '6月',\r\n      '7月', '8月', '9月', '10月', '11月', '12月'\r\n    ]\r\n    const monthIndex = parseInt(month) - 1\r\n    return `${year}年${monthNames[monthIndex]}`\r\n  }\r\n\r\n  // 日期视图（月视图）\r\n  const date = new Date(dateStr)\r\n  const today = new Date()\r\n  const yesterday = new Date(today)\r\n  yesterday.setDate(yesterday.getDate() - 1)\r\n\r\n  // 格式化为 MM-DD 格式\r\n  const month = (date.getMonth() + 1).toString().padStart(2, '0')\r\n  const day = date.getDate().toString().padStart(2, '0')\r\n  const formatted = `${month}-${day}`\r\n\r\n  // 添加星期信息\r\n  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']\r\n  const weekday = weekdays[date.getDay()]\r\n\r\n  // 判断是否是今天或昨天\r\n  if (dateStr === formatDate(today)) {\r\n    return `${formatted} 今天 ${weekday}`\r\n  } else if (dateStr === formatDate(yesterday)) {\r\n    return `${formatted} 昨天 ${weekday}`\r\n  } else {\r\n    return `${formatted} ${weekday}`\r\n  }\r\n}\r\n\r\nfunction formatDate(date) {\r\n  const y = date.getFullYear()\r\n  const m = (date.getMonth() + 1).toString().padStart(2, '0')\r\n  const d = date.getDate().toString().padStart(2, '0')\r\n  return `${y}-${m}-${d}`\r\n}\r\n\r\nfunction getMonthDays(year, month) {\r\n  const days = new Date(year, month, 0).getDate()\r\n  const result = []\r\n  for (let i = 1; i <= days; i++) {\r\n    const d = new Date(year, month - 1, i)\r\n    result.push({date: formatDate(d)})\r\n  }\r\n  return result\r\n}\r\n\r\n// 初始化当前月份和年份\r\nconst now = new Date()\r\nselectedMonth.value = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`\r\nselectedYear.value = `${now.getFullYear()}`\r\ndateList.value = getMonthDays(now.getFullYear(), now.getMonth() + 1)\r\nconsole.log('初始化:', { selectedMonth: selectedMonth.value, selectedYear: selectedYear.value });\r\n\r\n// 页面显示时的生命周期\r\nonShow(() => {\r\n  console.log('页面显示，重新加载数据')\r\n  // 重新加载当前月份的数据\r\n  if (selectedMonth.value) {\r\n    loadRecords(selectedMonth.value)\r\n  }\r\n})\r\n\r\n/**\r\n * 切换视图模式\r\n * @param {string} mode - 'month' 或 'year'\r\n */\r\nfunction switchViewMode(mode) {\r\n  viewMode.value = mode\r\n  console.log('切换视图模式:', mode)\r\n\r\n  // 根据模式加载对应数据\r\n  if (mode === 'year') {\r\n    loadRecords(selectedYear.value)\r\n  } else {\r\n    loadRecords(selectedMonth.value)\r\n  }\r\n}\r\n\r\n/**\r\n * 统一的日期选择处理\r\n * @param {Event} e - 选择事件\r\n */\r\nfunction onDateChange(e) {\r\n  const selectedValue = e.detail.value\r\n  console.log('选择日期:', selectedValue, '当前模式:', viewMode.value)\r\n\r\n  if (viewMode.value === 'year') {\r\n    // 年视图：选择的是年份\r\n    selectedYear.value = selectedValue\r\n    loadRecords(selectedYear.value)\r\n  } else {\r\n    // 月视图：选择的是年-月\r\n    selectedMonth.value = selectedValue\r\n    const [y, m] = selectedValue.split('-').map(Number)\r\n    dateList.value = getMonthDays(y, m)\r\n    loadRecords(selectedMonth.value)\r\n  }\r\n}\r\n\r\n// 修改月份 (保留兼容性)\r\nfunction onMonthChange(e) {\r\n  onDateChange(e)\r\n}\r\n\r\n// 获取指定月份或年份列表\r\nasync function loadRecords(date) {\r\n  try {\r\n    const res = await wx.cloud.callFunction({\r\n      name: 'getRecordsByDate',\r\n      data: { date }\r\n    })\r\n\r\n    if (res.result.success) {\r\n      // 检查是否是年份查询\r\n      if (res.result.isYearQuery) {\r\n        // 年份查询：显示月度汇总数据\r\n        const monthlyData = res.result.monthlyData || []\r\n\r\n        // 构造年度视图：每个月作为一个项目\r\n        dateList.value = monthlyData.map(monthData => ({\r\n          date: monthData.month, // 格式: YYYY-MM\r\n          records: monthData.records,\r\n          totalAmount: monthData.totalAmount,\r\n          isMonthSummary: true\r\n        }))\r\n\r\n        console.log('年度数据:', dateList.value)\r\n      } else {\r\n        // 月份查询：显示日期详情\r\n        const rawData = res.result.data // 云函数返回的数据数组\r\n        const grouped = {} // 按日期分组\r\n\r\n        // 把同一天的记录聚合到数组\r\n        rawData.forEach(item => {\r\n          if (!grouped[item.date]) {\r\n            grouped[item.date] = []\r\n          }\r\n          grouped[item.date].push(item)\r\n        })\r\n\r\n        const [y, m] = date.split('-').map(Number)\r\n        const allDays = getMonthDays(y, m)\r\n\r\n        // 构造带 records 的完整月列表\r\n        dateList.value = allDays.map(day => ({\r\n          date: day.date,\r\n          records: grouped[day.date] || [],\r\n          isMonthSummary: false\r\n        }))\r\n\r\n        console.log('月度数据:', dateList.value)\r\n      }\r\n    } else {\r\n      console.log('查询失败:', res.result)\r\n      uni.showToast({ title: '查询失败', icon: 'none' })\r\n    }\r\n  } catch (error) {\r\n    console.log('调用失败:', error);\r\n    uni.showToast({ title: '调用失败', icon: 'none' })\r\n  }\r\n}\r\n\r\n/**\r\n * 处理列表项点击事件\r\n * @param {Object} item - 点击的项目\r\n */\r\nfunction handleItemClick(item) {\r\n  if (item.isMonthSummary) {\r\n    // 年视图：点击月份，切换到该月的月视图\r\n    selectedMonth.value = item.date\r\n    viewMode.value = 'month'\r\n    const [y, m] = item.date.split('-').map(Number)\r\n    dateList.value = getMonthDays(y, m)\r\n    loadRecords(selectedMonth.value)\r\n  } else {\r\n    // 月视图：点击日期，跳转到详情页\r\n    goToDay(item.date)\r\n  }\r\n}\r\n\r\n/**\r\n * 跳转到日期详情页\r\n * @param {string} date - 日期字符串\r\n */\r\nfunction goToDay(date) {\r\n  uni.navigateTo({\r\n    url: `/pages/days/day?date=${date}`\r\n  })\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 全局样式 */\r\npage {\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.container {\r\n  padding: 24rpx;\r\n  background-color: #f8f9fa;\r\n  min-height: 100vh;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 模式选择器样式 */\r\n.mode-selector {\r\n  display: flex;\r\n  background-color: #ffffff;\r\n  border-radius: 12rpx;\r\n  margin-bottom: 16rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\r\n  overflow: hidden;\r\n}\r\n\r\n.mode-button {\r\n  flex: 1;\r\n  padding: 20rpx;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #6c757d;\r\n  background-color: #f8f9fa;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.mode-button.active {\r\n  background-color: #007aff;\r\n  color: #ffffff;\r\n}\r\n\r\n/* 日期选择器样式 */\r\n.date-picker {\r\n  padding: 24rpx 32rpx;\r\n  background-color: #ffffff;\r\n  border: 1rpx solid #e9ecef;\r\n  border-radius: 12rpx;\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n  margin-bottom: 24rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\r\n  text-align: center;\r\n}\r\n\r\n/* 保留原有样式兼容性 */\r\n.month-picker {\r\n  padding: 24rpx 32rpx;\r\n  background-color: #ffffff;\r\n  border: 1rpx solid #e9ecef;\r\n  border-radius: 12rpx;\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n  margin-bottom: 24rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\r\n  text-align: center;\r\n}\r\n\r\n/* 日期列表样式 */\r\n.date-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12rpx;\r\n  flex: 1;\r\n}\r\n\r\n/* 日期项样式 */\r\n.date-item {\r\n  padding: 24rpx;\r\n  border-radius: 12rpx;\r\n  background-color: #ffffff;\r\n  border: 1rpx solid #e9ecef;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\r\n  transition: all 0.2s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.date-item:active {\r\n  transform: scale(0.98);\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n/* 有记录的日期项特殊样式 */\r\n.date-item.has-records {\r\n  border-left: 4rpx solid #007aff;\r\n}\r\n\r\n/* 日期头部样式 */\r\n.date-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n/* 日期文字样式 */\r\n.date-text {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n/* 总计徽章样式 */\r\n.total-badge {\r\n  background: linear-gradient(135deg, #007aff, #0056d3);\r\n  color: #ffffff;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  padding: 6rpx 16rpx;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\r\n}\r\n\r\n/* 无记录样式 */\r\n.no-records {\r\n  text-align: center;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.no-records-text {\r\n  font-size: 26rpx;\r\n  color: #adb5bd;\r\n  font-style: italic;\r\n}\r\n/* 记录列表样式 */\r\n.record-list {\r\n  margin-top: 12rpx;\r\n  padding-top: 12rpx;\r\n  border-top: 1rpx solid #f1f3f4;\r\n}\r\n\r\n/* 记录项样式 */\r\n.record-item {\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.record-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 记录摘要样式 */\r\n.record-summary {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12rpx 16rpx;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8rpx;\r\n  border: 1rpx solid #e9ecef;\r\n}\r\n\r\n/* 用户名样式 */\r\n.user-name {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  flex: 1;\r\n}\r\n\r\n/* 金额样式 */\r\n.amount {\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n  color: #28a745;\r\n  background-color: #d4edda;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 16rpx;\r\n  border: 1rpx solid #c3e6cb;\r\n}\r\n</style>", "import MiniProgramPage from '/Users/<USER>/Projects/zhong333/zhong333/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "month", "onShow", "wx", "MiniProgramPage"], "mappings": ";;;;;AAyEA,UAAM,WAAWA,cAAG,IAAC,EAAE;AACvB,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,UAAM,eAAeA,cAAG,IAAC,EAAE;AAC3B,UAAM,WAAWA,cAAG,IAAC,OAAO;AAG5B,aAAS,SAAS,gBAAgB;AAChC,UAAI;AACF,cAAM,OAAO,KAAK,MAAM,cAAc;AACtCC,sBAAAA,MAAY,MAAA,OAAA,+BAAA,IAAI;AAGhB,cAAM,QAAQ,OAAO,OAAO,IAAI,EAAE,OAAO,CAAC,KAAK,QAAQ;AACrD,gBAAM,MAAM,WAAW,GAAG;AAC1B,iBAAO,OAAO,MAAM,GAAG,IAAI,IAAI;AAAA,QAChC,GAAE,CAAC;AAEJ,eAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,MAC/B,SAAQ,GAAG;AACV,eAAO;AAAA,MACR;AAAA,IACH;AAGA,aAAS,YAAY,SAAS;AAC5B,YAAM,QAAQ,QAAQ,OAAO,CAAC,KAAK,WAAW;AAC5C,eAAO,MAAM,SAAS,OAAO,WAAW;AAAA,MACzC,GAAE,CAAC;AACJ,aAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,IAChC;AAGA,aAAS,kBAAkB,SAAS,iBAAiB,OAAO;AAE1D,UAAI,gBAAgB;AAClB,cAAM,CAAC,MAAMC,MAAK,IAAI,QAAQ,MAAM,GAAG;AACvC,cAAM,aAAa;AAAA,UACjB;AAAA,UAAM;AAAA,UAAM;AAAA,UAAM;AAAA,UAAM;AAAA,UAAM;AAAA,UAC9B;AAAA,UAAM;AAAA,UAAM;AAAA,UAAM;AAAA,UAAO;AAAA,UAAO;AAAA,QACjC;AACD,cAAM,aAAa,SAASA,MAAK,IAAI;AACrC,eAAO,GAAG,IAAI,IAAI,WAAW,UAAU,CAAC;AAAA,MACzC;AAGD,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAM,QAAQ,oBAAI,KAAM;AACxB,YAAM,YAAY,IAAI,KAAK,KAAK;AAChC,gBAAU,QAAQ,UAAU,QAAO,IAAK,CAAC;AAGzC,YAAM,SAAS,KAAK,SAAU,IAAG,GAAG,WAAW,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACrD,YAAM,YAAY,GAAG,KAAK,IAAI,GAAG;AAGjC,YAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,YAAM,UAAU,SAAS,KAAK,OAAM,CAAE;AAGtC,UAAI,YAAY,WAAW,KAAK,GAAG;AACjC,eAAO,GAAG,SAAS,OAAO,OAAO;AAAA,MAClC,WAAU,YAAY,WAAW,SAAS,GAAG;AAC5C,eAAO,GAAG,SAAS,OAAO,OAAO;AAAA,MACrC,OAAS;AACL,eAAO,GAAG,SAAS,IAAI,OAAO;AAAA,MAC/B;AAAA,IACH;AAEA,aAAS,WAAW,MAAM;AACxB,YAAM,IAAI,KAAK,YAAa;AAC5B,YAAM,KAAK,KAAK,SAAU,IAAG,GAAG,WAAW,SAAS,GAAG,GAAG;AAC1D,YAAM,IAAI,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACnD,aAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IACvB;AAEA,aAAS,aAAa,MAAM,OAAO;AACjC,YAAM,OAAO,IAAI,KAAK,MAAM,OAAO,CAAC,EAAE,QAAS;AAC/C,YAAM,SAAS,CAAE;AACjB,eAAS,IAAI,GAAG,KAAK,MAAM,KAAK;AAC9B,cAAM,IAAI,IAAI,KAAK,MAAM,QAAQ,GAAG,CAAC;AACrC,eAAO,KAAK,EAAC,MAAM,WAAW,CAAC,EAAC,CAAC;AAAA,MAClC;AACD,aAAO;AAAA,IACT;AAGA,UAAM,MAAM,oBAAI,KAAM;AACtB,kBAAc,QAAQ,GAAG,IAAI,YAAa,CAAA,KAAK,IAAI,SAAQ,IAAK,GAAG,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAC9F,iBAAa,QAAQ,GAAG,IAAI,YAAa,CAAA;AACzC,aAAS,QAAQ,aAAa,IAAI,YAAa,GAAE,IAAI,SAAU,IAAG,CAAC;AACnED,kBAAA,MAAA,MAAA,OAAA,gCAAY,QAAQ,EAAE,eAAe,cAAc,OAAO,cAAc,aAAa,MAAO,CAAA;AAG5FE,kBAAAA,OAAO,MAAM;AACXF,oBAAAA,MAAY,MAAA,OAAA,gCAAA,aAAa;AAEzB,UAAI,cAAc,OAAO;AACvB,oBAAY,cAAc,KAAK;AAAA,MAChC;AAAA,IACH,CAAC;AAMD,aAAS,eAAe,MAAM;AAC5B,eAAS,QAAQ;AACjBA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,WAAW,IAAI;AAG3B,UAAI,SAAS,QAAQ;AACnB,oBAAY,aAAa,KAAK;AAAA,MAClC,OAAS;AACL,oBAAY,cAAc,KAAK;AAAA,MAChC;AAAA,IACH;AAMA,aAAS,aAAa,GAAG;AACvB,YAAM,gBAAgB,EAAE,OAAO;AAC/BA,uEAAY,SAAS,eAAe,SAAS,SAAS,KAAK;AAE3D,UAAI,SAAS,UAAU,QAAQ;AAE7B,qBAAa,QAAQ;AACrB,oBAAY,aAAa,KAAK;AAAA,MAClC,OAAS;AAEL,sBAAc,QAAQ;AACtB,cAAM,CAAC,GAAG,CAAC,IAAI,cAAc,MAAM,GAAG,EAAE,IAAI,MAAM;AAClD,iBAAS,QAAQ,aAAa,GAAG,CAAC;AAClC,oBAAY,cAAc,KAAK;AAAA,MAChC;AAAA,IACH;AAQA,mBAAe,YAAY,MAAM;AAC/B,UAAI;AACF,cAAM,MAAM,MAAMG,mBAAG,MAAM,aAAa;AAAA,UACtC,MAAM;AAAA,UACN,MAAM,EAAE,KAAM;AAAA,QACpB,CAAK;AAED,YAAI,IAAI,OAAO,SAAS;AAEtB,cAAI,IAAI,OAAO,aAAa;AAE1B,kBAAM,cAAc,IAAI,OAAO,eAAe,CAAE;AAGhD,qBAAS,QAAQ,YAAY,IAAI,gBAAc;AAAA,cAC7C,MAAM,UAAU;AAAA;AAAA,cAChB,SAAS,UAAU;AAAA,cACnB,aAAa,UAAU;AAAA,cACvB,gBAAgB;AAAA,YAC1B,EAAU;AAEFH,0BAAY,MAAA,MAAA,OAAA,gCAAA,SAAS,SAAS,KAAK;AAAA,UAC3C,OAAa;AAEL,kBAAM,UAAU,IAAI,OAAO;AAC3B,kBAAM,UAAU,CAAE;AAGlB,oBAAQ,QAAQ,UAAQ;AACtB,kBAAI,CAAC,QAAQ,KAAK,IAAI,GAAG;AACvB,wBAAQ,KAAK,IAAI,IAAI,CAAE;AAAA,cACxB;AACD,sBAAQ,KAAK,IAAI,EAAE,KAAK,IAAI;AAAA,YACtC,CAAS;AAED,kBAAM,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM,GAAG,EAAE,IAAI,MAAM;AACzC,kBAAM,UAAU,aAAa,GAAG,CAAC;AAGjC,qBAAS,QAAQ,QAAQ,IAAI,UAAQ;AAAA,cACnC,MAAM,IAAI;AAAA,cACV,SAAS,QAAQ,IAAI,IAAI,KAAK,CAAE;AAAA,cAChC,gBAAgB;AAAA,YAC1B,EAAU;AAEFA,0BAAY,MAAA,MAAA,OAAA,gCAAA,SAAS,SAAS,KAAK;AAAA,UACpC;AAAA,QACP,OAAW;AACLA,wBAAY,MAAA,MAAA,OAAA,gCAAA,SAAS,IAAI,MAAM;AAC/BA,wBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,QAC9C;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,OAAA,gCAAY,SAAS,KAAK;AAC1BA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,MAC9C;AAAA,IACH;AAMA,aAAS,gBAAgB,MAAM;AAC7B,UAAI,KAAK,gBAAgB;AAEvB,sBAAc,QAAQ,KAAK;AAC3B,iBAAS,QAAQ;AACjB,cAAM,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,MAAM,GAAG,EAAE,IAAI,MAAM;AAC9C,iBAAS,QAAQ,aAAa,GAAG,CAAC;AAClC,oBAAY,cAAc,KAAK;AAAA,MACnC,OAAS;AAEL,gBAAQ,KAAK,IAAI;AAAA,MAClB;AAAA,IACH;AAMA,aAAS,QAAQ,MAAM;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wBAAwB,IAAI;AAAA,MACrC,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5SA,GAAG,WAAWI,SAAe;"}