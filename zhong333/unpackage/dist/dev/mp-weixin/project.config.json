{"description": "项目配置文件。", "packOptions": {"ignore": []}, "setting": {"urlCheck": false, "es6": true, "postcss": false, "minified": false, "newFeature": true, "bigPackageSizeSupport": true, "minifyWXML": true}, "compileType": "miniprogram", "libVersion": "2.20.0", "appid": "wxeb4719c92f7a1957", "projectname": "zhong333", "cloudfunctionRoot": "cloudfunctions", "cloudbaseRoot": "", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}