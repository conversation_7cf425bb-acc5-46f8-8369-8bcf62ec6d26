"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "month",
  setup(__props) {
    const records = common_vendor.ref([]);
    const userGroups = common_vendor.ref([]);
    const currentMonth = common_vendor.ref("");
    const monthTitle = common_vendor.computed(() => {
      if (!currentMonth.value)
        return "";
      const [year, monthNum] = currentMonth.value.split("-");
      return `${year}年${parseInt(monthNum)}月`;
    });
    const monthTotal = common_vendor.computed(() => {
      return userGroups.value.reduce((sum, group) => {
        return sum + parseFloat(group.total || 0);
      }, 0).toFixed(2);
    });
    const totalRecords = common_vendor.computed(() => {
      return records.value.length;
    });
    const activeDays = common_vendor.computed(() => {
      const days = /* @__PURE__ */ new Set();
      records.value.forEach((record) => {
        if (record.date) {
          days.add(record.date);
        }
      });
      return days.size;
    });
    const formatRecordDate = (date) => {
      const dateObj = new Date(date);
      const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
      const day = dateObj.getDate().toString().padStart(2, "0");
      const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      const weekday = weekdays[dateObj.getDay()];
      return `${month}-${day} ${weekday}`;
    };
    const parseDescription = (description) => {
      try {
        return JSON.parse(description || "{}");
      } catch {
        return {};
      }
    };
    const getRecordTotal = (description) => {
      try {
        const data = JSON.parse(description || "{}");
        const total = Object.values(data).reduce((sum, val) => {
          const num = parseFloat(val);
          return sum + (isNaN(num) ? 0 : num);
        }, 0);
        return total.toFixed(2);
      } catch {
        return "0.00";
      }
    };
    const loadMonthData = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/month/month.vue:152", "加载月度数据:", currentMonth.value);
        common_vendor.index.showLoading({ title: "加载中..." });
        const res = await common_vendor.wx$1.cloud.callFunction({
          name: "getRecordsByDate",
          data: { date: currentMonth.value }
        });
        common_vendor.index.hideLoading();
        if (res.result.success) {
          records.value = res.result.data || [];
          common_vendor.index.__f__("log", "at pages/month/month.vue:165", "月度记录:", records.value);
          groupRecordsByUser();
        } else {
          common_vendor.index.__f__("log", "at pages/month/month.vue:170", "查询失败:", res.result);
          common_vendor.index.showToast({
            title: res.result.error || "查询失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/month/month.vue:177", "调用失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    };
    const groupRecordsByUser = () => {
      const groups = {};
      records.value.forEach((record) => {
        const userName = record.name || "未知用户";
        if (!groups[userName]) {
          groups[userName] = {
            name: userName,
            records: [],
            total: 0
          };
        }
        groups[userName].records.push(record);
        groups[userName].total += parseFloat(getRecordTotal(record.description));
      });
      userGroups.value = Object.values(groups).map((group) => ({
        ...group,
        total: group.total.toFixed(2),
        records: group.records.sort((a, b) => b.date.localeCompare(a.date))
        // 按日期倒序
      })).sort((a, b) => parseFloat(b.total) - parseFloat(a.total));
      common_vendor.index.__f__("log", "at pages/month/month.vue:216", "用户分组:", userGroups.value);
    };
    common_vendor.onMounted(() => {
      const pages = common_vendor.index.getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;
      if (options && options.month) {
        currentMonth.value = options.month;
        common_vendor.index.__f__("log", "at pages/month/month.vue:228", "获取到月份参数:", currentMonth.value);
        loadMonthData();
      } else {
        common_vendor.index.showToast({
          title: "缺少月份参数",
          icon: "none"
        });
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(monthTitle.value),
        b: common_vendor.t(monthTotal.value),
        c: common_vendor.t(totalRecords.value),
        d: common_vendor.t(activeDays.value),
        e: common_vendor.f(userGroups.value, (userGroup, index, i0) => {
          return {
            a: common_vendor.t(userGroup.name),
            b: common_vendor.t(userGroup.total),
            c: common_vendor.f(userGroup.records, (record, rIndex, i1) => {
              return {
                a: common_vendor.t(formatRecordDate(record.date)),
                b: common_vendor.t(getRecordTotal(record.description)),
                c: common_vendor.f(parseDescription(record.description), (amount, category, i2) => {
                  return {
                    a: common_vendor.t(category),
                    b: common_vendor.t(amount),
                    c: category
                  };
                }),
                d: record._id
              };
            }),
            d: userGroup.name
          };
        }),
        f: userGroups.value.length === 0
      }, userGroups.value.length === 0 ? {} : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/month/month.js.map
