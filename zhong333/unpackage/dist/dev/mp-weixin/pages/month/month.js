"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "month",
  setup(__props) {
    const records = common_vendor.ref([]);
    const userGroups = common_vendor.ref([]);
    const currentMonth = common_vendor.ref("");
    const monthTitle = common_vendor.computed(() => {
      if (!currentMonth.value)
        return "";
      const [year, monthNum] = currentMonth.value.split("-");
      return `${year}年${parseInt(monthNum)}月`;
    });
    const monthTotal = common_vendor.computed(() => {
      return userGroups.value.reduce((sum, group) => {
        return sum + parseFloat(group.total || 0);
      }, 0).toFixed(2);
    });
    const totalRecords = common_vendor.computed(() => {
      return records.value.length;
    });
    const activeDays = common_vendor.computed(() => {
      const days = /* @__PURE__ */ new Set();
      records.value.forEach((record) => {
        if (record.date) {
          days.add(record.date);
        }
      });
      return days.size;
    });
    const loadMonthData = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/month/month.vue:101", "加载月度数据:", currentMonth.value);
        common_vendor.index.showLoading({ title: "加载中..." });
        const res = await common_vendor.wx$1.cloud.callFunction({
          name: "getRecordsByDate",
          data: { date: currentMonth.value }
        });
        common_vendor.index.hideLoading();
        if (res.result.success) {
          records.value = res.result.data || [];
          common_vendor.index.__f__("log", "at pages/month/month.vue:114", "月度记录:", records.value);
          groupRecordsByUser();
        } else {
          common_vendor.index.__f__("log", "at pages/month/month.vue:119", "查询失败:", res.result);
          common_vendor.index.showToast({
            title: res.result.error || "查询失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/month/month.vue:126", "调用失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    };
    const groupRecordsByUser = () => {
      const groups = {};
      records.value.forEach((record) => {
        const userName = record.name || "未知用户";
        if (!groups[userName]) {
          groups[userName] = {
            name: userName,
            records: [],
            categories: {},
            // 按类型汇总
            total: 0
          };
        }
        groups[userName].records.push(record);
        try {
          const description = JSON.parse(record.description || "{}");
          Object.entries(description).forEach(([category, amount]) => {
            const numAmount = parseFloat(amount) || 0;
            if (!groups[userName].categories[category]) {
              groups[userName].categories[category] = {
                total: 0,
                count: 0
              };
            }
            groups[userName].categories[category].total += numAmount;
            groups[userName].categories[category].count += 1;
            groups[userName].total += numAmount;
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/month/month.vue:173", "解析记录描述失败:", error);
        }
      });
      userGroups.value = Object.values(groups).map((group) => ({
        ...group,
        total: group.total.toFixed(2),
        // 将categories对象转换为数组，按金额排序
        categories: Object.entries(group.categories).map(([name, data]) => ({
          name,
          total: data.total.toFixed(2),
          count: data.count
        })).sort((a, b) => parseFloat(b.total) - parseFloat(a.total)).reduce((obj, item) => {
          obj[item.name] = {
            total: item.total,
            count: item.count
          };
          return obj;
        }, {})
      })).sort((a, b) => parseFloat(b.total) - parseFloat(a.total));
      common_vendor.index.__f__("log", "at pages/month/month.vue:200", "用户分组和类型汇总:", userGroups.value);
    };
    common_vendor.onMounted(() => {
      const pages = common_vendor.index.getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;
      if (options && options.month) {
        currentMonth.value = options.month;
        common_vendor.index.__f__("log", "at pages/month/month.vue:212", "获取到月份参数:", currentMonth.value);
        loadMonthData();
      } else {
        common_vendor.index.showToast({
          title: "缺少月份参数",
          icon: "none"
        });
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(monthTitle.value),
        b: common_vendor.t(monthTotal.value),
        c: common_vendor.t(totalRecords.value),
        d: common_vendor.t(activeDays.value),
        e: common_vendor.f(userGroups.value, (userGroup, index, i0) => {
          return {
            a: common_vendor.t(userGroup.name),
            b: common_vendor.t(userGroup.total),
            c: common_vendor.f(userGroup.categories, (categoryData, categoryName, i1) => {
              return {
                a: common_vendor.t(categoryName),
                b: common_vendor.t(categoryData.count),
                c: common_vendor.t(categoryData.total),
                d: categoryName
              };
            }),
            d: userGroup.name
          };
        }),
        f: userGroups.value.length === 0
      }, userGroups.value.length === 0 ? {} : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/month/month.js.map
