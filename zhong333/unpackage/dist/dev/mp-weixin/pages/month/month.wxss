
/* 全局样式 */
page {
  background-color: #f8f9fa;
}
.container {
  padding: 24rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
  text-align: center;
  color: #2c3e50;
}

/* 月度概览样式 */
.month-overview {
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.overview-label {
  font-size: 26rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}
.overview-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

/* 用户分组样式 */
.user-groups {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.user-group {
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* 用户头部样式 */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #007aff;
  color: #ffffff;
}
.user-name {
  font-size: 32rpx;
  font-weight: 600;
}
.user-total {
  font-size: 30rpx;
  font-weight: 700;
}

/* 用户分类汇总样式 */
.user-categories {
  padding: 16rpx;
}
.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 12rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #28a745;
  transition: all 0.2s ease;
}
.category-item:hover {
  background-color: #e9ecef;
  transform: translateX(4rpx);
}
.category-item:last-child {
  margin-bottom: 0;
}

/* 分类信息区域 */
.category-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.category-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4rpx;
}
.category-count {
  font-size: 24rpx;
  color: #6c757d;
}

/* 分类总金额 */
.category-total {
  font-size: 32rpx;
  font-weight: 700;
  color: #28a745;
  background-color: #d4edda;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid #c3e6cb;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}
.empty-text {
  font-size: 30rpx;
  color: #adb5bd;
}
