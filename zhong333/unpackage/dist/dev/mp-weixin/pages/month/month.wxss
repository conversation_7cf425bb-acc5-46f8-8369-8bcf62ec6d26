
/* 全局样式 */
page {
  background-color: #f8f9fa;
}
.container {
  padding: 24rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
  text-align: center;
  color: #2c3e50;
}

/* 月度概览样式 */
.month-overview {
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.overview-label {
  font-size: 26rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}
.overview-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

/* 用户分组样式 */
.user-groups {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.user-group {
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* 用户头部样式 */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #007aff;
  color: #ffffff;
}
.user-name {
  font-size: 32rpx;
  font-weight: 600;
}
.user-total {
  font-size: 30rpx;
  font-weight: 700;
}

/* 用户记录样式 */
.user-records {
  padding: 16rpx;
}
.record-item {
  margin-bottom: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #28a745;
}
.record-item:last-child {
  margin-bottom: 0;
}

/* 记录头部样式 */
.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.record-date {
  font-size: 28rpx;
  font-weight: 500;
  color: #495057;
}
.record-amount {
  font-size: 28rpx;
  font-weight: 700;
  color: #28a745;
}

/* 记录详情样式 */
.record-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 12rpx;
  background-color: #ffffff;
  border-radius: 6rpx;
}
.category-name {
  font-size: 26rpx;
  color: #6c757d;
}
.category-amount {
  font-size: 26rpx;
  font-weight: 500;
  color: #495057;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}
.empty-text {
  font-size: 30rpx;
  color: #adb5bd;
}
