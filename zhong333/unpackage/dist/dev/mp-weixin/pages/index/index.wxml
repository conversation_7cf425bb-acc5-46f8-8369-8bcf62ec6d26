<view class="container"><view class="mode-selector"><view class="{{['mode-button', a && 'active']}}" bindtap="{{b}}"> 月视图 </view><view class="{{['mode-button', c && 'active']}}" bindtap="{{d}}"> 年视图 </view></view><picker mode="date" fields="{{f}}" bindchange="{{g}}"><view class="date-picker">{{e}}</view></picker><view class="date-list"><view wx:for="{{h}}" wx:for-item="item" wx:key="f" bindtap="{{item.g}}" class="{{['date-item', item.h && 'has-records']}}"><view class="date-header"><text class="date-text">{{item.a}}</text><view wx:if="{{item.b}}" class="total-badge"> ¥{{item.c}}</view></view><view wx:if="{{item.d}}" class="record-list"><view wx:for="{{item.e}}" wx:for-item="record" wx:key="c" class="record-item"><view class="record-summary"><text class="user-name">{{record.a}}</text><text class="amount">¥{{record.b}}</text></view></view></view><view wx:else class="no-records"><text class="no-records-text">暂无记录</text></view></view></view></view>