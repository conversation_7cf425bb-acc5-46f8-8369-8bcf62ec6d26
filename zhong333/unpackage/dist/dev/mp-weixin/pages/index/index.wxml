<view class="container"><picker mode="date" fields="month" bindchange="{{b}}"><view class="month-picker">选择月份：{{a}}</view></picker><view class="date-list"><view wx:for="{{c}}" wx:for-item="item" wx:key="f" bindtap="{{item.g}}" class="{{['date-item', item.h && 'has-records']}}"><view class="date-header"><text class="date-text">{{item.a}}</text><view wx:if="{{item.b}}" class="total-badge"> ¥{{item.c}}</view></view><view wx:if="{{item.d}}" class="record-list"><view wx:for="{{item.e}}" wx:for-item="record" wx:key="c" class="record-item"><view class="record-summary"><text class="user-name">{{record.a}}</text><text class="amount">¥{{record.b}}</text></view></view></view><view wx:else class="no-records"><text class="no-records-text">暂无记录</text></view></view></view></view>