<view class="container"><view class="mode-selector"><view class="{{['mode-button', a && 'active']}}" bindtap="{{b}}"> 月视图 </view><view class="{{['mode-button', c && 'active']}}" bindtap="{{d}}"> 年视图 </view></view><picker mode="date" fields="{{f}}" bindchange="{{g}}"><view class="date-picker">{{e}}</view></picker><view class="date-list"><view wx:for="{{h}}" wx:for-item="item" wx:key="m" bindtap="{{item.n}}" class="{{['date-item', item.o && 'has-records']}}"><view class="date-header"><view class="date-info"><text class="date-text">{{item.a}}</text><text wx:if="{{item.b}}" class="record-count">{{item.c}}条记录 </text></view><view wx:if="{{item.d}}" class="total-badge"> ¥{{item.e}}</view></view><view wx:if="{{item.f}}" class="month-summary"><view class="summary-stats"><view class="stat-item"><text class="stat-label">记录数</text><text class="stat-value">{{item.g}}条</text></view><view class="stat-item"><text class="stat-label">用户数</text><text class="stat-value">{{item.h}}人</text></view><view class="stat-item"><text class="stat-label">天数</text><text class="stat-value">{{item.i}}天</text></view></view><view class="user-summary"><view wx:for="{{item.j}}" wx:for-item="userStat" wx:key="c" class="user-stat-item"><text class="user-name">{{userStat.a}}</text><text class="user-amount">¥{{userStat.b}}</text></view></view></view><view wx:elif="{{item.k}}" class="record-list"><view wx:for="{{item.l}}" wx:for-item="record" wx:key="c" class="record-item"><view class="record-summary"><text class="user-name">{{record.a}}</text><text class="amount">¥{{record.b}}</text></view></view></view><view wx:else class="no-records"><text class="no-records-text">暂无记录</text></view></view></view></view>