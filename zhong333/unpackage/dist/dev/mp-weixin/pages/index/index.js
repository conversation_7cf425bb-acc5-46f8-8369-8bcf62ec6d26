"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const dateList = common_vendor.ref([]);
    const selectedMonth = common_vendor.ref("");
    function getTotal(descriptionStr) {
      try {
        const data = JSON.parse(descriptionStr);
        common_vendor.index.__f__("log", "at pages/index/index.vue:56", data);
        const total = Object.values(data).reduce((sum, val) => {
          const num = parseFloat(val);
          return sum + (isNaN(num) ? 0 : num);
        }, 0);
        return Number(total.toFixed(2));
      } catch (e) {
        return 0;
      }
    }
    function getDayTotal(records) {
      const total = records.reduce((sum, record) => {
        return sum + getTotal(record.description);
      }, 0);
      return Number(total.toFixed(2));
    }
    function formatDisplayDate(dateStr) {
      const date = new Date(dateStr);
      const today = /* @__PURE__ */ new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const formatted = `${month}-${day}`;
      const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      const weekday = weekdays[date.getDay()];
      if (dateStr === formatDate(today)) {
        return `${formatted} 今天 ${weekday}`;
      } else if (dateStr === formatDate(yesterday)) {
        return `${formatted} 昨天 ${weekday}`;
      } else {
        return `${formatted} ${weekday}`;
      }
    }
    function formatDate(date) {
      const y = date.getFullYear();
      const m = (date.getMonth() + 1).toString().padStart(2, "0");
      const d = date.getDate().toString().padStart(2, "0");
      return `${y}-${m}-${d}`;
    }
    function getMonthDays(year, month) {
      const days = new Date(year, month, 0).getDate();
      const result = [];
      for (let i = 1; i <= days; i++) {
        const d = new Date(year, month - 1, i);
        result.push({ date: formatDate(d) });
      }
      return result;
    }
    const now = /* @__PURE__ */ new Date();
    selectedMonth.value = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, "0")}`;
    dateList.value = getMonthDays(now.getFullYear(), now.getMonth() + 1);
    common_vendor.index.__f__("log", "at pages/index/index.vue:125", selectedMonth.value);
    common_vendor.onShow(() => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:129", "页面显示，重新加载数据");
      if (selectedMonth.value) {
        loadRecords(selectedMonth.value);
      }
    });
    function onMonthChange(e) {
      selectedMonth.value = e.detail.value;
      const [y, m] = selectedMonth.value.split("-").map(Number);
      dateList.value = getMonthDays(y, m);
      loadRecords(selectedMonth.value);
    }
    async function loadRecords(date) {
      try {
        const res = await common_vendor.wx$1.cloud.callFunction({
          name: "getRecordsByDate",
          data: { date }
        });
        if (res.result.success) {
          if (res.result.isYearQuery) {
            const monthlyData = res.result.monthlyData || [];
            dateList.value = monthlyData.map((monthData) => ({
              date: monthData.month,
              // 格式: YYYY-MM
              records: monthData.records,
              totalAmount: monthData.totalAmount,
              isMonthSummary: true
            }));
            common_vendor.index.__f__("log", "at pages/index/index.vue:166", "年度数据:", dateList.value);
          } else {
            const rawData = res.result.data;
            const grouped = {};
            rawData.forEach((item) => {
              if (!grouped[item.date]) {
                grouped[item.date] = [];
              }
              grouped[item.date].push(item);
            });
            const [y, m] = date.split("-").map(Number);
            const allDays = getMonthDays(y, m);
            dateList.value = allDays.map((day) => ({
              date: day.date,
              records: grouped[day.date] || [],
              isMonthSummary: false
            }));
            common_vendor.index.__f__("log", "at pages/index/index.vue:190", "月度数据:", dateList.value);
          }
        } else {
          common_vendor.index.__f__("log", "at pages/index/index.vue:193", "查询失败:", res.result);
          common_vendor.index.showToast({ title: "查询失败", icon: "none" });
        }
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:197", "调用失败:", error);
        common_vendor.index.showToast({ title: "调用失败", icon: "none" });
      }
    }
    function goToDay(date) {
      common_vendor.index.navigateTo({
        url: `/pages/days/day?date=${date}`
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(selectedMonth.value),
        b: common_vendor.o(onMonthChange),
        c: common_vendor.f(dateList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(formatDisplayDate(item.date, item.isMonthSummary)),
            b: item.records && item.records.length > 0
          }, item.records && item.records.length > 0 ? {
            c: common_vendor.t(item.isMonthSummary ? item.totalAmount : getDayTotal(item.records))
          } : {}, {
            d: item.records && item.records.length > 0
          }, item.records && item.records.length > 0 ? {
            e: common_vendor.f(item.records, (record, rIndex, i1) => {
              return {
                a: common_vendor.t(record.name),
                b: common_vendor.t(getTotal(record.description)),
                c: rIndex
              };
            })
          } : {}, {
            f: index,
            g: common_vendor.o(($event) => goToDay(item.date), index),
            h: item.records && item.records.length > 0 ? 1 : ""
          });
        })
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
