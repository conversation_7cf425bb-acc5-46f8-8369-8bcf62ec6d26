"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "day",
  props: ["date"],
  setup(__props) {
    const props = __props;
    const records = common_vendor.ref([
      // 示例数据结构（实际数据从云函数加载）
      // {
      //   _id: "zhong333-2025-06-27",
      //   name: "zhong333",
      //   date: "2025-06-27",
      //   description: '{"面包":105.29,"外卖":45,"水果":20}',
      //   createTime: "2025-06-27T10:00:00.000Z",
      //   updateTime: "2025-06-27T10:00:00.000Z"
      // },
      // {
      //   _id: "YPP-2025-06-27",
      //   name: "YPP",
      //   date: "2025-06-27",
      //   description: '{"面包":15,"外卖":45,"水果":20}',
      //   createTime: "2025-06-27T10:00:00.000Z",
      //   updateTime: "2025-06-27T10:00:00.000Z"
      // }
    ]);
    async function loadRecords(date) {
      common_vendor.index.__f__("log", "at pages/days/day.vue:129", "加载数据:", date);
      try {
        const res = await common_vendor.wx$1.cloud.callFunction({
          name: "getRecordsByDate",
          data: { date }
        });
        if (res.result.success) {
          const rawData = res.result.data;
          common_vendor.index.__f__("log", "at pages/days/day.vue:138", "查询成功:", rawData);
          records.value = rawData;
          initializeEditingStates();
          common_vendor.index.__f__("log", "at pages/days/day.vue:144", "parsedDescriptions:", records.value, parsedDescriptions.value);
        } else {
          common_vendor.index.__f__("log", "at pages/days/day.vue:147", "查询失败:", res.result);
          common_vendor.index.showToast({ title: "查询失败", icon: "none" });
        }
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/days/day.vue:151", "调用失败:", error);
        common_vendor.index.showToast({ title: "调用失败", icon: "none" });
      }
    }
    if (props.date) {
      loadRecords(props.date);
    } else {
      const today = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
      loadRecords(today);
    }
    const parsedDescriptions = common_vendor.computed(() => {
      return records.value.map((r) => {
        try {
          return JSON.parse(r.description || "{}");
        } catch {
          return {};
        }
      });
    });
    const editingNames = common_vendor.ref([]);
    const newItemKey = common_vendor.ref([]);
    const newItemValue = common_vendor.ref([]);
    const editingKeys = common_vendor.ref([]);
    const initializeEditingStates = () => {
      const recordCount = records.value.length;
      editingNames.value = parsedDescriptions.value.map(
        (desc) => Object.fromEntries(Object.keys(desc).map((k) => [k, k]))
      );
      newItemKey.value = Array(recordCount).fill("");
      newItemValue.value = Array(recordCount).fill(0);
      editingKeys.value = Array(recordCount).fill({});
      addingKey.value = Array(recordCount).fill(false);
      showOptions.value = Array(recordCount).fill({});
      showNewItemOptions.value = Array(recordCount).fill(false);
      common_vendor.index.__f__("log", "at pages/days/day.vue:206", "编辑状态初始化完成，记录数量:", recordCount);
    };
    const toggleEdit = (index, key) => {
      var _a, _b, _c;
      const newKey = editingNames.value[index][key];
      const current = parsedDescriptions.value[index][key];
      if ((_a = editingKeys.value[index]) == null ? void 0 : _a[key]) {
        if (Number(current) === 0) {
          delete parsedDescriptions.value[index][key];
          delete editingNames.value[index][key];
        } else if (newKey !== key && newKey.trim()) {
          parsedDescriptions.value[index][newKey] = current;
          delete parsedDescriptions.value[index][key];
          editingNames.value[index][newKey] = newKey;
          delete editingNames.value[index][key];
          delete editingKeys.value[index][key];
          return;
        }
      }
      editingKeys.value[index] = {
        ...editingKeys.value[index],
        [key]: !((_b = editingKeys.value[index]) == null ? void 0 : _b[key])
      };
      if (!((_c = editingKeys.value[index]) == null ? void 0 : _c[key])) {
        showOptions.value[index] = {
          ...showOptions.value[index],
          [key]: false
        };
      }
    };
    const addingKey = common_vendor.ref([]);
    const categoryOptions = common_vendor.ref([
      "地铁",
      "早饭",
      "午饭",
      "晚饭"
    ]);
    const showOptions = common_vendor.ref([]);
    const showNewItemOptions = common_vendor.ref([]);
    const showEditOptions = (index, category) => {
      showOptions.value[index] = {
        ...showOptions.value[index],
        [category]: true
      };
    };
    const hideEditOptions = (index, category) => {
      setTimeout(() => {
        showOptions.value[index] = {
          ...showOptions.value[index],
          [category]: false
        };
      }, 200);
    };
    const selectOption = (index, category, option) => {
      editingNames.value[index][category] = option;
      showOptions.value[index] = {
        ...showOptions.value[index],
        [category]: false
      };
    };
    const selectNewItemOption = (index, option) => {
      newItemKey.value[index] = option;
      showNewItemOptions.value[index] = false;
    };
    const hideNewItemOptions = (index) => {
      setTimeout(() => {
        showNewItemOptions.value[index] = false;
      }, 200);
    };
    const startAdd = (index) => {
      addingKey.value[index] = true;
    };
    const confirmAdd = (index) => {
      var _a;
      const key = (_a = newItemKey.value[index]) == null ? void 0 : _a.trim();
      const value = newItemValue.value[index];
      if (!key) {
        common_vendor.index.showToast({ title: "项目名称不能为空", icon: "none" });
        return;
      }
      if (isNaN(value)) {
        common_vendor.index.showToast({ title: "金额必须是数字", icon: "none" });
        return;
      }
      parsedDescriptions.value[index][key] = value;
      newItemKey.value[index] = "";
      newItemValue.value[index] = 0;
      addingKey.value[index] = false;
    };
    const saveAll = async () => {
      var _a;
      try {
        common_vendor.index.showLoading({ title: "保存中..." });
        const updatedRecords = records.value.map((record, idx) => ({
          ...record,
          description: JSON.stringify(parsedDescriptions.value[idx]),
          updateTime: (/* @__PURE__ */ new Date()).toISOString()
        }));
        common_vendor.index.__f__("log", "at pages/days/day.vue:393", "准备保存的数据：", updatedRecords);
        common_vendor.index.__f__("log", "at pages/days/day.vue:394", "第一条记录详情：", JSON.stringify(updatedRecords[0], null, 2));
        const res = await common_vendor.wx$1.cloud.callFunction({
          name: "postRecordsByDate",
          data: {
            records: updatedRecords,
            date: props.date
          }
        });
        common_vendor.index.hideLoading();
        if (res.result && res.result.success) {
          records.value = updatedRecords;
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success",
            duration: 2e3
          });
          common_vendor.index.__f__("log", "at pages/days/day.vue:418", "保存成功：", res.result);
        } else {
          common_vendor.index.__f__("log", "at pages/days/day.vue:421", "保存失败:", res.result);
          common_vendor.index.showToast({
            title: ((_a = res.result) == null ? void 0 : _a.message) || "保存失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/days/day.vue:429", "保存出错:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    };
    const addNewUser = () => {
      const userOptions = [
        { name: "zhong333" },
        { name: "YPP" }
      ];
      const existingNames = records.value.map((record) => record.name);
      const availableUsers = userOptions.filter((user) => !existingNames.includes(user.name));
      if (availableUsers.length === 0) {
        common_vendor.index.showToast({
          title: "所有用户都已添加",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      const userNames = availableUsers.map((user) => user.name);
      common_vendor.index.showActionSheet({
        itemList: userNames,
        success: (res) => {
          const selectedUser = availableUsers[res.tapIndex];
          const newRecord = {
            _id: `${selectedUser.name}-${props.date}`,
            name: selectedUser.name,
            date: props.date || (/* @__PURE__ */ new Date()).toISOString().split("T")[0],
            description: "{}",
            // 空的记录
            createTime: (/* @__PURE__ */ new Date()).toISOString(),
            updateTime: (/* @__PURE__ */ new Date()).toISOString()
          };
          records.value.push(newRecord);
          initializeEditingStates();
          common_vendor.index.__f__("log", "at pages/days/day.vue:486", "新增用户记录:", newRecord);
          common_vendor.index.showToast({
            title: `新增${selectedUser.name}成功`,
            icon: "none",
            duration: 2e3
          });
        },
        fail: () => {
          common_vendor.index.__f__("log", "at pages/days/day.vue:495", "用户取消选择");
        }
      });
    };
    const getTotal = (index) => {
      const desc = parsedDescriptions == null ? void 0 : parsedDescriptions.value[index];
      return parsedDescriptions.value.length > 0 ? Object.values(desc).reduce((sum, v) => sum + Number(v || 0), 0).toFixed(2) : 0;
    };
    const overallTotal = common_vendor.computed(() => {
      return parsedDescriptions.value.reduce((sum, desc) => {
        return sum + Object.values(desc).reduce((s, v) => s + Number(v || 0), 0);
      }, 0).toFixed(2);
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(props.date || "今日"),
        b: common_vendor.f(records.value, (record, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(record.name),
            b: common_vendor.f(parsedDescriptions.value[index], (amount, category, i1) => {
              var _a, _b, _c, _d, _e, _f;
              return common_vendor.e({
                a: (_a = editingKeys.value[index]) == null ? void 0 : _a[category]
              }, ((_b = editingKeys.value[index]) == null ? void 0 : _b[category]) ? common_vendor.e({
                b: common_vendor.o(($event) => showEditOptions(index, category), category),
                c: common_vendor.o(($event) => hideEditOptions(index, category), category),
                d: editingNames.value[index][category],
                e: common_vendor.o(($event) => editingNames.value[index][category] = $event.detail.value, category),
                f: (_c = showOptions.value[index]) == null ? void 0 : _c[category]
              }, ((_d = showOptions.value[index]) == null ? void 0 : _d[category]) ? {
                g: common_vendor.f(categoryOptions.value, (option, k2, i2) => {
                  return {
                    a: common_vendor.t(option),
                    b: option,
                    c: common_vendor.o(($event) => selectOption(index, category, option), option)
                  };
                })
              } : {}) : {
                h: common_vendor.t(category)
              }, {
                i: (_e = editingKeys.value[index]) == null ? void 0 : _e[category]
              }, ((_f = editingKeys.value[index]) == null ? void 0 : _f[category]) ? {
                j: category,
                k: parsedDescriptions.value[index][category],
                l: common_vendor.o(($event) => parsedDescriptions.value[index][category] = $event.detail.value, category),
                m: common_vendor.o(($event) => toggleEdit(index, category), category)
              } : {
                n: common_vendor.t(parsedDescriptions.value[index][category]),
                o: common_vendor.o(($event) => toggleEdit(index, category), category)
              }, {
                p: category
              });
            }),
            c: !addingKey.value[index]
          }, !addingKey.value[index] ? {
            d: common_vendor.o(($event) => startAdd(index), record._id)
          } : common_vendor.e({
            e: common_vendor.o(($event) => showNewItemOptions.value[index] = true, record._id),
            f: common_vendor.o(($event) => hideNewItemOptions(index), record._id),
            g: newItemKey.value[index],
            h: common_vendor.o(($event) => newItemKey.value[index] = $event.detail.value, record._id),
            i: showNewItemOptions.value[index]
          }, showNewItemOptions.value[index] ? {
            j: common_vendor.f(categoryOptions.value, (option, k1, i1) => {
              return {
                a: common_vendor.t(option),
                b: option,
                c: common_vendor.o(($event) => selectNewItemOption(index, option), option)
              };
            })
          } : {}, {
            k: newItemValue.value[index],
            l: common_vendor.o(($event) => newItemValue.value[index] = $event.detail.value, record._id),
            m: common_vendor.o(($event) => confirmAdd(index), record._id)
          }), {
            n: common_vendor.t(getTotal(index)),
            o: record._id
          });
        }),
        c: common_vendor.t(overallTotal.value),
        d: common_vendor.o(addNewUser),
        e: common_vendor.o(saveAll)
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/days/day.js.map
