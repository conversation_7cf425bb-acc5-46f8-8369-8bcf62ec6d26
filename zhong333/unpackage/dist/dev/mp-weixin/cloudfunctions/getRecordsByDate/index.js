const cloud = require('wx-server-sdk')
cloud.init()

exports.main = async (event, context) => {
  const db = cloud.database()
  const wxContext = cloud.getWXContext()
  const { date } = event

  try {
    let whereClause = {
      // openid: wxContext.OPENID
    }

    if (/^\d{4}$/.test(date)) {
      // 传的是年份，如 2025，查询整年数据
      whereClause.date = db.RegExp({
        regexp: `^${date}`,
        options: 'i'
      })
    } else if (/^\d{4}-\d{2}$/.test(date)) {
      // 传的是年月，如 2025-06，使用正则模糊匹配
      whereClause.date = db.RegExp({
        regexp: `^${date}`,
        options: 'i'
      })
    } else if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      // 传的是年月日，精确匹配
      whereClause.date = date
    } else {
      // 不符合格式，直接返回空或报错
      return {
        success: false,
        error: '日期格式错误，请传 YYYY、YYYY-MM 或 YYYY-MM-DD'
      }
    }

    const res = await db.collection('records')
      .where(whereClause)
      .orderBy('date', 'desc')
      .get()

    // 如果是年份查询，按月份分组数据
    if (/^\d{4}$/.test(date)) {
      const monthlyData = {}

      res.data.forEach(record => {
        const month = record.date.substring(0, 7) // 提取 YYYY-MM
        if (!monthlyData[month]) {
          monthlyData[month] = []
        }
        monthlyData[month].push(record)
      })

      // 转换为数组格式，包含月份和该月的记录
      const monthlyArray = Object.keys(monthlyData)
        .sort((a, b) => b.localeCompare(a)) // 按月份倒序排列
        .map(month => ({
          month: month,
          records: monthlyData[month],
          totalAmount: monthlyData[month].reduce((sum, record) => {
            try {
              const desc = JSON.parse(record.description || '{}')
              return sum + Object.values(desc).reduce((s, v) => s + Number(v || 0), 0)
            } catch {
              return sum
            }
          }, 0).toFixed(2)
        }))

      return {
        success: true,
        data: res.data,
        monthlyData: monthlyArray,
        isYearQuery: true
      }
    }

    return {
      success: true,
      data: res.data,
      isYearQuery: false
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}