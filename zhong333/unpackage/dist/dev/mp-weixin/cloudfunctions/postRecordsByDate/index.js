const cloud = require('wx-server-sdk')
cloud.init()

const db = cloud.database()

/**
 * 保存记录的云函数
 * 支持新增和更新记录
 */
exports.main = async (event, context) => {
  const { records, date } = event

  try {
    console.log('接收到保存请求:', { records, date })

    // 参数验证
    if (!records || !Array.isArray(records)) {
      return {
        success: false,
        message: '记录数据格式错误'
      }
    }

    if (!date) {
      return {
        success: false,
        message: '日期参数缺失'
      }
    }

    // 获取数据库集合
    const collection = db.collection('records')

    // 批量处理记录
    const results = []
    const errors = []

    for (const record of records) {
      try {
        // 验证记录数据
        if (!record._id || !record.name || !record.date) {
          errors.push({
            record: record._id || 'unknown',
            error: '记录数据不完整'
          })
          continue
        }

        // 准备保存的数据（不包含_id，因为_id是文档ID）
        const saveData = {
          name: record.name,
          date: record.date,
          description: record.description || '{}',
          updateTime: new Date().toISOString()
        }

        // 如果是新记录，添加创建时间
        if (record.createTime) {
          saveData.createTime = record.createTime
        } else {
          saveData.createTime = new Date().toISOString()
        }

        console.log('准备保存的数据:', JSON.stringify(saveData))

        // 验证saveData
        if (!saveData || typeof saveData !== 'object') {
          throw new Error('saveData不是有效对象')
        }

        // 尝试更新记录，如果不存在则创建
        await collection.doc(record._id).set({
          data: saveData
        })

        results.push({
          _id: record._id,
          success: true,
          operation: 'upsert'
        })

        console.log(`记录 ${record._id} 保存成功`)

      } catch (recordError) {
        console.error(`保存记录 ${record._id} 失败:`, recordError)
        errors.push({
          record: record._id || 'unknown',
          error: recordError.message
        })
      }
    }

    // 返回结果
    const response = {
      success: errors.length === 0,
      message: errors.length === 0 ? '保存成功' : `部分记录保存失败`,
      data: {
        total: records.length,
        success: results.length,
        failed: errors.length,
        results: results,
        errors: errors
      }
    }

    console.log('保存结果:', response)
    return response

  } catch (error) {
    console.error('云函数执行失败:', error)
    return {
      success: false,
      message: '服务器错误: ' + error.message,
      error: error.toString()
    }
  }
}