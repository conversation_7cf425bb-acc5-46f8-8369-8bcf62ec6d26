"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/days/day.js";
  "./pages/month/month.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:4", "App Launch");
    if (!common_vendor.wx$1.cloud) {
      common_vendor.index.__f__("error", "at App.vue:6", "请使用 2.2.3 及以上基础库以支持云开发");
      return;
    }
    common_vendor.wx$1.cloud.init({
      env: "cloudbase-4gc39cjdb0cbb5e2",
      traceUser: true
    });
    const cachedOpenId = common_vendor.wx$1.getStorageSync("openid");
    if (!cachedOpenId) {
      common_vendor.wx$1.cloud.callFunction({
        name: "getOpenId",
        success: (res) => {
          common_vendor.wx$1.setStorageSync("openid", res.result.openid);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at App.vue:22", "获取openid失败", err);
        }
      });
    } else {
      common_vendor.index.__f__("log", "at App.vue:26", "缓存的openid:", cachedOpenId);
    }
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:30", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:33", "App Hide");
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
