<template>
  <view class="container">
    <!-- 视图模式选择 -->
    <view class="mode-selector">
      <view
        class="mode-button"
        :class="{ active: viewMode === 'month' }"
        @click="switchViewMode('month')"
      >
        月视图
      </view>
      <view
        class="mode-button"
        :class="{ active: viewMode === 'year' }"
        @click="switchViewMode('year')"
      >
        年视图
      </view>
    </view>

    <!-- 日期选择器 -->
    <picker
      mode="date"
      :fields="viewMode === 'year' ? 'year' : 'month'"
      @change="onDateChange"
    >
      <view class="date-picker">
        {{ viewMode === 'year' ? selectedYear + '年' : '选择月份：' + selectedMonth }}
      </view>
    </picker>
    <view class="date-list">
      <view
        class="date-item"
        v-for="(item, index) in dateList"
        :key="index"
        @click="handleItemClick(item)"
        :class="{ 'has-records': item.records && item.records.length > 0 }"
      >
        <view class="date-header">
          <view class="date-info">
            <text class="date-text">{{ formatDisplayDate(item.date, item.isMonthSummary) }}</text>
            <text v-if="item.isMonthSummary && item.recordCount" class="record-count">
              {{ item.recordCount }}条记录
            </text>
          </view>
          <view v-if="item.records && item.records.length > 0" class="total-badge">
            ¥{{ item.isMonthSummary ? item.totalAmount : getDayTotal(item.records) }}
          </view>
        </view>

        <!-- 年视图：显示月度汇总信息 -->
        <view v-if="item.isMonthSummary && item.records && item.records.length > 0" class="month-summary">
          <view class="summary-stats">
            <view class="stat-item">
              <text class="stat-label">记录数</text>
              <text class="stat-value">{{ item.recordCount }}条</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">用户数</text>
              <text class="stat-value">{{ getUniqueUsers(item.records).length }}人</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">天数</text>
              <text class="stat-value">{{ getUniqueDays(item.records).length }}天</text>
            </view>
          </view>

          <!-- 用户汇总 -->
          <view class="user-summary">
            <view
              v-for="userStat in getUserStats(item.records)"
              :key="userStat.name"
              class="user-stat-item"
            >
              <text class="user-name">{{ userStat.name }}</text>
              <text class="user-amount">¥{{ userStat.total }}</text>
            </view>
          </view>
        </view>

        <!-- 月视图：显示日期详细记录 -->
        <view
          v-else-if="!item.isMonthSummary && item.records && item.records.length > 0"
          class="record-list"
        >
          <view
            v-for="(record, rIndex) in item.records"
            :key="rIndex"
            class="record-item"
          >
            <view class="record-summary">
              <text class="user-name">{{ record.name }}</text>
              <text class="amount">¥{{ getTotal(record.description) }}</text>
            </view>
          </view>
        </view>

        <view v-else class="no-records">
          <text class="no-records-text">暂无记录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'

const dateList = ref([])
const selectedMonth = ref('')
const selectedYear = ref('')
const viewMode = ref('month') // 'month' 或 'year'

// 计算单个记录总数
function getTotal(descriptionStr) {
  try {
    const data = JSON.parse(descriptionStr)
    console.log(data);

    // 确保每个值是数字
    const total = Object.values(data).reduce((sum, val) => {
      const num = parseFloat(val)
      return sum + (isNaN(num) ? 0 : num)
    }, 0)

    return Number(total.toFixed(2)) // 输出数字类型，保留两位小数
  } catch (e) {
    return 0
  }
}

// 计算一天所有记录的总数
function getDayTotal(records) {
  const total = records.reduce((sum, record) => {
    return sum + getTotal(record.description)
  }, 0)
  return Number(total.toFixed(2))
}

// 格式化显示日期
function formatDisplayDate(dateStr, isMonthSummary = false) {
  // 如果是月度汇总（年视图）
  if (isMonthSummary) {
    const [year, month] = dateStr.split('-')
    const monthNames = [
      '1月', '2月', '3月', '4月', '5月', '6月',
      '7月', '8月', '9月', '10月', '11月', '12月'
    ]
    const monthIndex = parseInt(month) - 1
    return `${year}年${monthNames[monthIndex]}`
  }

  // 日期视图（月视图）
  const date = new Date(dateStr)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  // 格式化为 MM-DD 格式
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const formatted = `${month}-${day}`

  // 添加星期信息
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const weekday = weekdays[date.getDay()]

  // 判断是否是今天或昨天
  if (dateStr === formatDate(today)) {
    return `${formatted} 今天 ${weekday}`
  } else if (dateStr === formatDate(yesterday)) {
    return `${formatted} 昨天 ${weekday}`
  } else {
    return `${formatted} ${weekday}`
  }
}

// 获取记录中的唯一用户
function getUniqueUsers(records) {
  const users = new Set()
  records.forEach(record => {
    if (record.name) {
      users.add(record.name)
    }
  })
  return Array.from(users)
}

// 获取记录中的唯一日期
function getUniqueDays(records) {
  const days = new Set()
  records.forEach(record => {
    if (record.date) {
      days.add(record.date)
    }
  })
  return Array.from(days)
}

// 获取用户统计信息
function getUserStats(records) {
  const userStats = {}

  records.forEach(record => {
    const userName = record.name || '未知用户'
    const amount = getTotal(record.description)

    if (!userStats[userName]) {
      userStats[userName] = {
        name: userName,
        total: 0,
        count: 0
      }
    }

    userStats[userName].total += amount
    userStats[userName].count += 1
  })

  // 转换为数组并按金额排序
  return Object.values(userStats)
    .map(stat => ({
      ...stat,
      total: stat.total.toFixed(2)
    }))
    .sort((a, b) => parseFloat(b.total) - parseFloat(a.total))
}

function formatDate(date) {
  const y = date.getFullYear()
  const m = (date.getMonth() + 1).toString().padStart(2, '0')
  const d = date.getDate().toString().padStart(2, '0')
  return `${y}-${m}-${d}`
}

function getMonthDays(year, month) {
  const days = new Date(year, month, 0).getDate()
  const result = []
  for (let i = 1; i <= days; i++) {
    const d = new Date(year, month - 1, i)
    result.push({date: formatDate(d)})
  }
  return result
}

// 初始化当前月份和年份
const now = new Date()
selectedMonth.value = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`
selectedYear.value = `${now.getFullYear()}`
dateList.value = getMonthDays(now.getFullYear(), now.getMonth() + 1)
console.log('初始化:', { selectedMonth: selectedMonth.value, selectedYear: selectedYear.value });

// 页面显示时的生命周期
onShow(() => {
  console.log('页面显示，重新加载数据')
  // 重新加载当前月份的数据
  if (selectedMonth.value) {
    loadRecords(selectedMonth.value)
  }
})

/**
 * 切换视图模式
 * @param {string} mode - 'month' 或 'year'
 */
function switchViewMode(mode) {
  viewMode.value = mode
  console.log('切换视图模式:', mode)

  // 根据模式加载对应数据
  if (mode === 'year') {
    loadRecords(selectedYear.value)
  } else {
    loadRecords(selectedMonth.value)
  }
}

/**
 * 统一的日期选择处理
 * @param {Event} e - 选择事件
 */
function onDateChange(e) {
  const selectedValue = e.detail.value
  console.log('选择日期:', selectedValue, '当前模式:', viewMode.value)

  if (viewMode.value === 'year') {
    // 年视图：选择的是年份
    selectedYear.value = selectedValue
    loadRecords(selectedYear.value)
  } else {
    // 月视图：选择的是年-月
    selectedMonth.value = selectedValue
    const [y, m] = selectedValue.split('-').map(Number)
    dateList.value = getMonthDays(y, m)
    loadRecords(selectedMonth.value)
  }
}

// 修改月份 (保留兼容性)
function onMonthChange(e) {
  onDateChange(e)
}

// 获取指定月份或年份列表
async function loadRecords(date) {
  try {
    console.log('开始加载数据:', date, '当前视图模式:', viewMode.value)

    uni.showLoading({ title: '加载中...' })

    const res = await wx.cloud.callFunction({
      name: 'getRecordsByDate',
      data: { date }
    })

    uni.hideLoading()

    if (res.result.success) {
      console.log('云函数返回结果:', res.result)

      // 检查是否是年份查询
      if (res.result.isYearQuery && viewMode.value === 'year') {
        // 年份查询：显示月度汇总数据
        const monthlyData = res.result.monthlyData || []

        console.log('处理年度数据:', monthlyData)

        // 构造年度视图：每个月作为一个项目
        dateList.value = monthlyData.map(monthData => ({
          date: monthData.month, // 格式: YYYY-MM
          records: monthData.records,
          totalAmount: monthData.totalAmount,
          recordCount: monthData.records.length,
          isMonthSummary: true
        }))

        console.log('年度视图数据:', dateList.value)

        if (dateList.value.length === 0) {
          uni.showToast({ title: '该年暂无记录', icon: 'none' })
        }

      } else {
        // 月份查询：显示日期详情
        const rawData = res.result.data // 云函数返回的数据数组
        const grouped = {} // 按日期分组

        console.log('处理月度数据:', rawData)

        // 把同一天的记录聚合到数组
        rawData.forEach(item => {
          if (!grouped[item.date]) {
            grouped[item.date] = []
          }
          grouped[item.date].push(item)
        })

        const [y, m] = date.split('-').map(Number)
        const allDays = getMonthDays(y, m)

        // 构造带 records 的完整月列表
        dateList.value = allDays.map(day => ({
          date: day.date,
          records: grouped[day.date] || [],
          isMonthSummary: false
        }))

        console.log('月度视图数据:', dateList.value)
      }
    } else {
      console.log('查询失败:', res.result)
      uni.showToast({
        title: res.result.error || '查询失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.log('调用失败:', error);
    uni.hideLoading()
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  }
}

/**
 * 处理列表项点击事件
 * @param {Object} item - 点击的项目
 */
function handleItemClick(item) {
  if (item.isMonthSummary) {
    // 年视图：点击月份，切换到该月的月视图
    selectedMonth.value = item.date
    viewMode.value = 'month'
    const [y, m] = item.date.split('-').map(Number)
    dateList.value = getMonthDays(y, m)
    loadRecords(selectedMonth.value)
  } else {
    // 月视图：点击日期，跳转到详情页
    goToDay(item.date)
  }
}

/**
 * 跳转到日期详情页
 * @param {string} date - 日期字符串
 */
function goToDay(date) {
  uni.navigateTo({
    url: `/pages/days/day?date=${date}`
  })
}
</script>

<style>
/* 全局样式 */
page {
  background-color: #f8f9fa;
}

.container {
  padding: 24rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 模式选择器样式 */
.mode-selector {
  display: flex;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.mode-button {
  flex: 1;
  padding: 20rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #6c757d;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.mode-button.active {
  background-color: #007aff;
  color: #ffffff;
}

/* 日期选择器样式 */
.date-picker {
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  text-align: center;
}

/* 保留原有样式兼容性 */
.month-picker {
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  text-align: center;
}

/* 日期列表样式 */
.date-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  flex: 1;
}

/* 日期项样式 */
.date-item {
  padding: 24rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.date-item:active {
  transform: scale(0.98);
  background-color: #f8f9fa;
}

/* 有记录的日期项特殊样式 */
.date-item.has-records {
  border-left: 4rpx solid #007aff;
}

/* 日期头部样式 */
.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

/* 日期信息区域 */
.date-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 日期文字样式 */
.date-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4rpx;
}

/* 记录数量样式 */
.record-count {
  font-size: 24rpx;
  color: #6c757d;
}

/* 总计徽章样式 */
.total-badge {
  background: linear-gradient(135deg, #007aff, #0056d3);
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 700;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

/* 月度汇总样式 */
.month-summary {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f1f3f4;
}

/* 统计数据样式 */
.summary-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

/* 用户汇总样式 */
.user-summary {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.user-stat-item .user-name {
  font-size: 28rpx;
  color: #495057;
  font-weight: 500;
}

.user-stat-item .user-amount {
  font-size: 28rpx;
  font-weight: 700;
  color: #28a745;
}

/* 无记录样式 */
.no-records {
  text-align: center;
  padding: 20rpx 0;
}

.no-records-text {
  font-size: 26rpx;
  color: #adb5bd;
  font-style: italic;
}
/* 记录列表样式 */
.record-list {
  margin-top: 12rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid #f1f3f4;
}

/* 记录项样式 */
.record-item {
  margin-bottom: 8rpx;
}

.record-item:last-child {
  margin-bottom: 0;
}

/* 记录摘要样式 */
.record-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

/* 用户名样式 */
.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #495057;
  flex: 1;
}

/* 金额样式 */
.amount {
  font-size: 28rpx;
  font-weight: 700;
  color: #28a745;
  background-color: #d4edda;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  border: 1rpx solid #c3e6cb;
}
</style>