<template>
  <view class="container">
    <picker mode="date" fields="month" @change="onMonthChange">
      <view class="month-picker">选择月份：{{ selectedMonth }}</view>
    </picker>
    <view class="date-list">
      <view
        class="date-item"
        v-for="(item, index) in dateList"
        :key="index"
        @click="goToDay(item.date)"
        :class="{ 'has-records': item.records && item.records.length > 0 }"
      >
        <view class="date-header">
          <text class="date-text">{{ formatDisplayDate(item.date, item.isMonthSummary) }}</text>
          <view v-if="item.records && item.records.length > 0" class="total-badge">
            ¥{{ item.isMonthSummary ? item.totalAmount : getDayTotal(item.records) }}
          </view>
        </view>

        <view
          v-if="item.records && item.records.length > 0"
          class="record-list"
        >
          <view
            v-for="(record, rIndex) in item.records"
            :key="rIndex"
            class="record-item"
          >
            <view class="record-summary">
              <text class="user-name">{{ record.name }}</text>
              <text class="amount">¥{{ getTotal(record.description) }}</text>
            </view>
          </view>
        </view>

        <view v-else class="no-records">
          <text class="no-records-text">暂无记录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'

const dateList = ref([])
const selectedMonth = ref('')

// 计算单个记录总数
function getTotal(descriptionStr) {
  try {
    const data = JSON.parse(descriptionStr)
    console.log(data);

    // 确保每个值是数字
    const total = Object.values(data).reduce((sum, val) => {
      const num = parseFloat(val)
      return sum + (isNaN(num) ? 0 : num)
    }, 0)

    return Number(total.toFixed(2)) // 输出数字类型，保留两位小数
  } catch (e) {
    return 0
  }
}

// 计算一天所有记录的总数
function getDayTotal(records) {
  const total = records.reduce((sum, record) => {
    return sum + getTotal(record.description)
  }, 0)
  return Number(total.toFixed(2))
}

// 格式化显示日期
function formatDisplayDate(dateStr) {
  const date = new Date(dateStr)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  // 格式化为 MM-DD 格式
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const formatted = `${month}-${day}`

  // 添加星期信息
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const weekday = weekdays[date.getDay()]

  // 判断是否是今天或昨天
  if (dateStr === formatDate(today)) {
    return `${formatted} 今天 ${weekday}`
  } else if (dateStr === formatDate(yesterday)) {
    return `${formatted} 昨天 ${weekday}`
  } else {
    return `${formatted} ${weekday}`
  }
}

function formatDate(date) {
  const y = date.getFullYear()
  const m = (date.getMonth() + 1).toString().padStart(2, '0')
  const d = date.getDate().toString().padStart(2, '0')
  return `${y}-${m}-${d}`
}

function getMonthDays(year, month) {
  const days = new Date(year, month, 0).getDate()
  const result = []
  for (let i = 1; i <= days; i++) {
    const d = new Date(year, month - 1, i)
    result.push({date: formatDate(d)})
  }
  return result
}

// 初始化当前月份
const now = new Date()
selectedMonth.value = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`
dateList.value = getMonthDays(now.getFullYear(), now.getMonth() + 1)
console.log(selectedMonth.value);

// 页面显示时的生命周期
onShow(() => {
  console.log('页面显示，重新加载数据')
  // 重新加载当前月份的数据
  if (selectedMonth.value) {
    loadRecords(selectedMonth.value)
  }
})

// 修改月份
function onMonthChange(e) {
  selectedMonth.value = e.detail.value
  const [y, m] = selectedMonth.value.split('-').map(Number)
  dateList.value = getMonthDays(y, m)
  loadRecords(selectedMonth.value)
}

// 获取指定月份或年份列表
async function loadRecords(date) {
  try {
    const res = await wx.cloud.callFunction({
      name: 'getRecordsByDate',
      data: { date }
    })

    if (res.result.success) {
      // 检查是否是年份查询
      if (res.result.isYearQuery) {
        // 年份查询：显示月度汇总数据
        const monthlyData = res.result.monthlyData || []

        // 构造年度视图：每个月作为一个项目
        dateList.value = monthlyData.map(monthData => ({
          date: monthData.month, // 格式: YYYY-MM
          records: monthData.records,
          totalAmount: monthData.totalAmount,
          isMonthSummary: true
        }))

        console.log('年度数据:', dateList.value)
      } else {
        // 月份查询：显示日期详情
        const rawData = res.result.data // 云函数返回的数据数组
        const grouped = {} // 按日期分组

        // 把同一天的记录聚合到数组
        rawData.forEach(item => {
          if (!grouped[item.date]) {
            grouped[item.date] = []
          }
          grouped[item.date].push(item)
        })

        const [y, m] = date.split('-').map(Number)
        const allDays = getMonthDays(y, m)

        // 构造带 records 的完整月列表
        dateList.value = allDays.map(day => ({
          date: day.date,
          records: grouped[day.date] || [],
          isMonthSummary: false
        }))

        console.log('月度数据:', dateList.value)
      }
    } else {
      console.log('查询失败:', res.result)
      uni.showToast({ title: '查询失败', icon: 'none' })
    }
  } catch (error) {
    console.log('调用失败:', error);
    uni.showToast({ title: '调用失败', icon: 'none' })
  }
}

function goToDay(date) {
  uni.navigateTo({
    url: `/pages/days/day?date=${date}`
  })
}
</script>

<style>
/* 全局样式 */
page {
  background-color: #f8f9fa;
}

.container {
  padding: 24rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 月份选择器样式 */
.month-picker {
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  text-align: center;
}

/* 日期列表样式 */
.date-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  flex: 1;
}

/* 日期项样式 */
.date-item {
  padding: 24rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.date-item:active {
  transform: scale(0.98);
  background-color: #f8f9fa;
}

/* 有记录的日期项特殊样式 */
.date-item.has-records {
  border-left: 4rpx solid #007aff;
}

/* 日期头部样式 */
.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

/* 日期文字样式 */
.date-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
}

/* 总计徽章样式 */
.total-badge {
  background: linear-gradient(135deg, #007aff, #0056d3);
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 700;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

/* 无记录样式 */
.no-records {
  text-align: center;
  padding: 20rpx 0;
}

.no-records-text {
  font-size: 26rpx;
  color: #adb5bd;
  font-style: italic;
}
/* 记录列表样式 */
.record-list {
  margin-top: 12rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid #f1f3f4;
}

/* 记录项样式 */
.record-item {
  margin-bottom: 8rpx;
}

.record-item:last-child {
  margin-bottom: 0;
}

/* 记录摘要样式 */
.record-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

/* 用户名样式 */
.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #495057;
  flex: 1;
}

/* 金额样式 */
.amount {
  font-size: 28rpx;
  font-weight: 700;
  color: #28a745;
  background-color: #d4edda;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  border: 1rpx solid #c3e6cb;
}
</style>