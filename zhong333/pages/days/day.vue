<template>
  <view class="container">
    <view class="title">{{ props.date || '今日' }} 的记账</view>

    <view
      v-for="(record, index) in records"
      :key="record._id"
      class="record-group"
    >
      <view>
        <view class="record-title"> {{ record.name }} 的记录：</view>
        <view
          v-for="(amount, category) in parsedDescriptions[index]"
          :key="category"
          class="record-item"
        >
          <template v-if="editingKeys[index]?.[category]">
            <view class="input-container">
              <input
                class="input"
                v-model="editingNames[index][category]"
                placeholder="项目名称"
                @focus="showEditOptions(index, category)"
                @blur="hideEditOptions(index, category)"
              />
              <view class="options-dropdown" v-if="showOptions[index]?.[category]">
                <view
                  v-for="option in categoryOptions"
                  :key="option"
                  class="option-item"
                  @click="selectOption(index, category, option)"
                >
                  {{ option }}
                </view>
              </view>
            </view>
          </template>
          <template v-else>
            <text class="record-text">{{ category }}</text>
          </template>
          <template v-if="editingKeys[index]?.[category]">
            <input
              class="input"
              type="digit"
              v-model="parsedDescriptions[index][category]"
              :placeholder="category"
            />
            <text @click="toggleEdit(index, category)">完成</text>
          </template>
          <template v-else>
            <text class="record-text">¥{{ parsedDescriptions[index][category] }}</text>
            <text @click="toggleEdit(index, category)">编辑</text>
          </template>
        </view>

        <view class="record-item" v-if="!addingKey[index]">
          <view class="add-item-button" @click="startAdd(index)">+ 新增</view>
        </view>
        <view class="record-item" v-else>
          <view class="input-container">
            <input
              class="input"
              v-model="newItemKey[index]"
              placeholder="新项目名称"
              @focus="showNewItemOptions[index] = true"
              @blur="hideNewItemOptions(index)"
            />
            <view class="options-dropdown" v-if="showNewItemOptions[index]">
              <view
                v-for="option in categoryOptions"
                :key="option"
                class="option-item"
                @click="selectNewItemOption(index, option)"
              >
                {{ option }}
              </view>
            </view>
          </view>
          <input class="input" type="digit" v-model="newItemValue[index]" placeholder="金额" />
          <text class="add-button" @click="confirmAdd(index)">确认</text>
        </view>

        <view class="record-item">
          <text class="record-text">总计</text>
          <text class="record-text">¥{{ getTotal(index) }}</text>
        </view>

      </view>
    </view>
    <view class="total-summary">
      总金额：¥{{ overallTotal }}
    </view>

    <view class="button-group">
      <view class="button add-user-button" @click="addNewUser">+ 新增用户</view>
      <view class="button save-button" @click="saveAll">保存</view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue";

const props = defineProps(['date'])

// 记账记录数据
const records = ref([
  // 示例数据结构（实际数据从云函数加载）
  // {
  //   _id: "zhong333-2025-06-27",
  //   name: "zhong333",
  //   date: "2025-06-27",
  //   description: '{"面包":105.29,"外卖":45,"水果":20}',
  //   createTime: "2025-06-27T10:00:00.000Z",
  //   updateTime: "2025-06-27T10:00:00.000Z"
  // },
  // {
  //   _id: "YPP-2025-06-27",
  //   name: "YPP",
  //   date: "2025-06-27",
  //   description: '{"面包":15,"外卖":45,"水果":20}',
  //   createTime: "2025-06-27T10:00:00.000Z",
  //   updateTime: "2025-06-27T10:00:00.000Z"
  // }
]);

// 获取指定月份列表
async function loadRecords(date) {
  console.log('加载数据:', date);
  
  try {
    const res = await wx.cloud.callFunction({
      name: 'getRecordsByDate',
      data: { date }
    })
    if (res.result.success) {
      const rawData = res.result.data
      console.log('查询成功:', rawData);
      records.value = rawData

      // 数据更新后，初始化相关状态
      initializeEditingStates();

      console.log('parsedDescriptions:', records.value, parsedDescriptions.value);

    } else {
      console.log('查询失败:', res.result)
      uni.showToast({ title: '查询失败', icon: 'none' })
    }
  } catch (error) {
    console.log('调用失败:', error);
    
    uni.showToast({ title: '调用失败', icon: 'none' })
  }
}
// 初始化页面数据
if (props.date) {
  loadRecords(props.date);
} else {
  // 如果没有传入日期，使用当前日期
  const today = new Date().toISOString().split('T')[0];
  loadRecords(today);
}

// 解析后的描述数据，将JSON字符串转换为对象
const parsedDescriptions = computed(() => {
  return records.value.map((r) => {
    try {
      return JSON.parse(r.description || '{}');
    } catch {
      return {};
    }
  });
});

// 编辑时的项目名称映射
const editingNames = ref([]);

// 新增项目的名称输入
const newItemKey = ref([]);
// 新增项目的金额输入
const newItemValue = ref([]);

// 编辑状态标记，记录哪些项目正在编辑
const editingKeys = ref([]);

/**
 * 初始化编辑相关的状态数组
 */
const initializeEditingStates = () => {
  const recordCount = records.value.length;

  // 初始化编辑名称映射
  editingNames.value = parsedDescriptions.value.map(desc =>
    Object.fromEntries(Object.keys(desc).map(k => [k, k]))
  );

  // 重置其他状态数组
  newItemKey.value = Array(recordCount).fill("");
  newItemValue.value = Array(recordCount).fill(0);
  editingKeys.value = Array(recordCount).fill({});
  addingKey.value = Array(recordCount).fill(false);
  showOptions.value = Array(recordCount).fill({});
  showNewItemOptions.value = Array(recordCount).fill(false);

  console.log('编辑状态初始化完成，记录数量:', recordCount);
};

/**
 * 切换编辑状态或保存编辑内容
 * @param {number} index - 记录的索引
 * @param {string} key - 要编辑的项目名称
 */
const toggleEdit = (index, key) => {
  const newKey = editingNames.value[index][key];
  const current = parsedDescriptions.value[index][key];

  // 如果当前处于编辑状态，则保存编辑内容
  if (editingKeys.value[index]?.[key]) {
    // 如果金额为0，删除该项目
    if (Number(current) === 0) {
      delete parsedDescriptions.value[index][key];
      delete editingNames.value[index][key];
    } else if (newKey !== key && newKey.trim()) {
      // 如果项目名称发生变化，更新项目名称
      parsedDescriptions.value[index][newKey] = current;
      delete parsedDescriptions.value[index][key];
      editingNames.value[index][newKey] = newKey;
      delete editingNames.value[index][key];
      delete editingKeys.value[index][key];
      return;
    }
  }

  // 切换编辑状态
  editingKeys.value[index] = {
    ...editingKeys.value[index],
    [key]: !editingKeys.value[index]?.[key],
  };

  // 如果退出编辑状态，隐藏选项下拉框
  if (!editingKeys.value[index]?.[key]) {
    showOptions.value[index] = {
      ...showOptions.value[index],
      [key]: false
    };
  }
};

// 添加新项目的状态标记
const addingKey = ref([]);

// 预设的项目名称选项
const categoryOptions = ref([
  '地铁', '早饭', '午饭', '晚饭'
]);

// 控制选项下拉框显示状态
const showOptions = ref([]);

// 控制新增项目选项下拉框显示状态
const showNewItemOptions = ref([]);

/**
 * 显示编辑选项下拉框
 * @param {number} index - 记录的索引
 * @param {string} category - 当前编辑的项目名称
 */
const showEditOptions = (index, category) => {
  showOptions.value[index] = {
    ...showOptions.value[index],
    [category]: true
  };
};

/**
 * 隐藏编辑选项下拉框（延迟执行以允许点击选项）
 * @param {number} index - 记录的索引
 * @param {string} category - 当前编辑的项目名称
 */
const hideEditOptions = (index, category) => {
  setTimeout(() => {
    showOptions.value[index] = {
      ...showOptions.value[index],
      [category]: false
    };
  }, 200);
};

/**
 * 选择预设选项
 * @param {number} index - 记录的索引
 * @param {string} category - 当前编辑的项目名称
 * @param {string} option - 选择的选项
 */
const selectOption = (index, category, option) => {
  editingNames.value[index][category] = option;
  showOptions.value[index] = {
    ...showOptions.value[index],
    [category]: false
  };
};

/**
 * 选择新增项目的预设选项
 * @param {number} index - 记录的索引
 * @param {string} option - 选择的选项
 */
const selectNewItemOption = (index, option) => {
  newItemKey.value[index] = option;
  showNewItemOptions.value[index] = false;
};

/**
 * 隐藏新增项目选项下拉框（延迟执行以允许点击选项）
 * @param {number} index - 记录的索引
 */
const hideNewItemOptions = (index) => {
  setTimeout(() => {
    showNewItemOptions.value[index] = false;
  }, 200);
};

/**
 * 开始添加新项目
 * @param {number} index - 记录的索引
 */
const startAdd = (index) => {
  addingKey.value[index] = true;
};

/**
 * 确认添加新项目
 * @param {number} index - 记录的索引
 */
const confirmAdd = (index) => {
  const key = newItemKey.value[index]?.trim();
  const value = newItemValue.value[index];

  // 验证项目名称不能为空
  if (!key) {
    uni.showToast({ title: "项目名称不能为空", icon: "none" });
    return;
  }

  // 验证金额必须是数字
  if (isNaN(value)) {
    uni.showToast({ title: "金额必须是数字", icon: "none" });
    return;
  }

  // 添加新项目到记录中
  parsedDescriptions.value[index][key] = value;
  // 重置输入框
  newItemKey.value[index] = "";
  newItemValue.value[index] = 0;
  // 退出添加状态
  addingKey.value[index] = false;
};

/**
 * 添加新项目（备用方法，当前未使用）
 * @param {number} index - 记录的索引
 */
const addItem = (index) => {
  const key = newItemKey.value[index]?.trim();
  const value = newItemValue.value[index];

  // 验证项目名称和金额有效性
  if (key && !isNaN(value)) {
    parsedDescriptions.value[index][key] = value;
    newItemKey.value[index] = "";
    newItemValue.value[index] = 0;
  }
};

/**
 * 保存所有记录到云端
 * 将解析后的描述数据重新序列化为JSON字符串并保存到云数据库
 */
const saveAll = async () => {
  try {
    // 显示保存提示
    uni.showLoading({ title: '保存中...' });

    // 准备要保存的数据
    const updatedRecords = records.value.map((record, idx) => ({
      ...record,
      description: JSON.stringify(parsedDescriptions.value[idx]),
      updateTime: new Date().toISOString()
    }));

    console.log("准备保存的数据：", updatedRecords);
    console.log("第一条记录详情：", JSON.stringify(updatedRecords[0], null, 2));

    // 调用云函数保存数据
    const res = await wx.cloud.callFunction({
      name: 'postRecordsByDate',
      data: {
        records: updatedRecords,
        date: props.date
      }
    });

    // 隐藏加载提示
    uni.hideLoading();

    if (res.result && res.result.success) {
      // 更新本地数据
      records.value = updatedRecords;

      uni.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 2000
      });

      console.log("保存成功：", res.result);

    } else {
      console.log('保存失败:', res.result);
      uni.showToast({
        title: res.result?.message || '保存失败',
        icon: 'none'
      });
    }

  } catch (error) {
    console.log('保存出错:', error);
    uni.hideLoading();
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    });
  }
};

/**
 * 新增用户记录
 * 为当前日期添加一个新的用户记录
 */
const addNewUser = () => {
  // 预设的用户列表
  const userOptions = [
    { name: 'zhong333' },
    { name: 'YPP' }
  ];

  // 检查当前已有的用户名
  const existingNames = records.value.map(record => record.name);
  const availableUsers = userOptions.filter(user => !existingNames.includes(user.name));

  if (availableUsers.length === 0) {
    uni.showToast({
      title: '所有用户都已添加',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  // 显示用户选择弹窗
  const userNames = availableUsers.map(user => user.name);

  uni.showActionSheet({
    itemList: userNames,
    success: (res) => {
      const selectedUser = availableUsers[res.tapIndex];

      // 创建新的记录
      const newRecord = {
        _id: `${selectedUser.name}-${props.date}`,
        name: selectedUser.name,
        date: props.date || new Date().toISOString().split('T')[0],
        description: '{}', // 空的记录
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      };

      // 添加到records数组
      records.value.push(newRecord);

      // 重新初始化编辑状态
      initializeEditingStates();

      console.log('新增用户记录:', newRecord);

      uni.showToast({
        title: `新增${selectedUser.name}成功`,
        icon: 'none',
        duration: 2000
      });
    },
    fail: () => {
      console.log('用户取消选择');
    }
  });
};

/**
 * 计算单个记录的总金额
 * @param {number} index - 记录的索引
 * @returns {string} 格式化后的总金额（保留两位小数）
 */
const getTotal = (index) => {
  const desc = parsedDescriptions?.value[index];
  return parsedDescriptions.value.length > 0 ? Object.values(desc).reduce((sum, v) => sum + Number(v || 0), 0).toFixed(2) : 0;
};

/**
 * 处理数字输入，确保支持小数点
 * @param {Event} e - 输入事件
 */
const handleNumberInput = (e) => {
  const value = e.detail.value;
  // 允许数字和小数点，移除其他字符
  const cleanValue = value.replace(/[^\d.]/g, '');
  // 确保只有一个小数点
  const parts = cleanValue.split('.');
  if (parts.length > 2) {
    return parts[0] + '.' + parts.slice(1).join('');
  }
  return cleanValue;
};

/**
 * 计算所有记录的总金额（计算属性）
 * @returns {string} 格式化后的总金额（保留两位小数）
 */
const overallTotal = computed(() => {
  return parsedDescriptions.value.reduce((sum, desc) => {
    return sum + Object.values(desc).reduce((s, v) => s + Number(v || 0), 0);
  }, 0).toFixed(2);
});
</script>

<style>
/* 全局样式 */
page {
  background-color: #f8f9fa;
}

.container {
  padding: 32rpx 24rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
  text-align: center;
  color: #2c3e50;
}

/* 输入框样式 */
.input {
  width: 180rpx;
  height: 56rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 12rpx;
  font-size: 28rpx;
  background-color: #ffffff;
  box-sizing: border-box;
}

.input:focus {
  border-color: #007aff;
  outline: none;
}

/* 操作按钮样式 */
.add-button {
  font-size: 28rpx;
  color: #007aff;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
}
/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 16rpx;
  margin: 32rpx 0;
}

.button {
  flex: 1;
  padding: 24rpx 20rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  text-align: center;
  color: #ffffff;
  border: none;
}

/* 保存按钮样式 */
.save-button {
  background-color: #007aff;
}

/* 新增用户按钮样式 */
.add-user-button {
  background-color: #28a745;
}
/* 记录组样式 */
.record-group {
  margin-bottom: 24rpx;
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #e9ecef;
}

.record-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #495057;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.record-item {
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-item:last-child {
  border-bottom: none;
}

.record-text {
  font-size: 28rpx;
  width: 140rpx;
  text-align: left;
  color: #495057;
}
/* 新增项目按钮样式 */
.add-item-button {
  color: #007aff;
  font-size: 28rpx;
  text-align: center;
  padding: 20rpx 0;
  width: 100%;
  background-color: #f8f9fa;
  border: 1rpx dashed #007aff;
  border-radius: 8rpx;
}

/* 总计样式 */
.total-summary {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
  margin: 32rpx 0;
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #e9ecef;
}

/* 输入容器样式 */
.input-container {
  position: relative;
  display: inline-block;
}

/* 选项下拉框样式 */
.options-dropdown {
  position: absolute;
  top: calc(100% + 4rpx);
  left: 0;
  right: 0;
  background-color: #ffffff;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300rpx;
  overflow-y: auto;
}

/* 选项项样式 */
.option-item {
  padding: 16rpx;
  font-size: 28rpx;
  color: #495057;
  border-bottom: 1rpx solid #f8f9fa;
}

.option-item:hover {
  background-color: #f8f9fa;
  color: #007aff;
}

.option-item:last-child {
  border-bottom: none;
}
</style>