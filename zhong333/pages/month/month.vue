<template>
  <view class="container">
    <view class="title">{{ monthTitle }} 月度详情</view>
    
    <!-- 月度统计概览 -->
    <view class="month-overview">
      <view class="overview-item">
        <text class="overview-label">总金额</text>
        <text class="overview-value">¥{{ monthTotal }}</text>
      </view>
      <view class="overview-item">
        <text class="overview-label">记录数</text>
        <text class="overview-value">{{ totalRecords }}条</text>
      </view>
      <view class="overview-item">
        <text class="overview-label">记账天数</text>
        <text class="overview-value">{{ activeDays }}天</text>
      </view>
    </view>

    <!-- 按人员分组显示 -->
    <view class="user-groups">
      <view 
        v-for="(userGroup, index) in userGroups" 
        :key="userGroup.name"
        class="user-group"
      >
        <view class="user-header">
          <text class="user-name">{{ userGroup.name }}</text>
          <text class="user-total">¥{{ userGroup.total }}</text>
        </view>
        
        <!-- 该用户的所有记录 -->
        <view class="user-records">
          <view 
            v-for="(record, rIndex) in userGroup.records" 
            :key="record._id"
            class="record-item"
          >
            <view class="record-header">
              <text class="record-date">{{ formatRecordDate(record.date) }}</text>
              <text class="record-amount">¥{{ getRecordTotal(record.description) }}</text>
            </view>
            
            <!-- 记录详情 -->
            <view class="record-details">
              <view 
                v-for="(amount, category) in parseDescription(record.description)" 
                :key="category"
                class="detail-item"
              >
                <text class="category-name">{{ category }}</text>
                <text class="category-amount">¥{{ amount }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="userGroups.length === 0" class="empty-state">
      <text class="empty-text">该月暂无记录</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const records = ref([])
const userGroups = ref([])
const currentMonth = ref('')

// 计算属性
const monthTitle = computed(() => {
  if (!currentMonth.value) return ''
  const [year, monthNum] = currentMonth.value.split('-')
  return `${year}年${parseInt(monthNum)}月`
})

const monthTotal = computed(() => {
  return userGroups.value.reduce((sum, group) => {
    return sum + parseFloat(group.total || 0)
  }, 0).toFixed(2)
})

const totalRecords = computed(() => {
  return records.value.length
})

const activeDays = computed(() => {
  const days = new Set()
  records.value.forEach(record => {
    if (record.date) {
      days.add(record.date)
    }
  })
  return days.size
})



/**
 * 格式化记录日期
 * @param {string} date - 日期字符串
 */
const formatRecordDate = (date) => {
  const dateObj = new Date(date)
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0')
  const day = dateObj.getDate().toString().padStart(2, '0')
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const weekday = weekdays[dateObj.getDay()]
  return `${month}-${day} ${weekday}`
}

/**
 * 解析记录描述
 * @param {string} description - 描述JSON字符串
 */
const parseDescription = (description) => {
  try {
    return JSON.parse(description || '{}')
  } catch {
    return {}
  }
}

/**
 * 计算单条记录总金额
 * @param {string} description - 描述JSON字符串
 */
const getRecordTotal = (description) => {
  try {
    const data = JSON.parse(description || '{}')
    const total = Object.values(data).reduce((sum, val) => {
      const num = parseFloat(val)
      return sum + (isNaN(num) ? 0 : num)
    }, 0)
    return total.toFixed(2)
  } catch {
    return '0.00'
  }
}

/**
 * 加载月度数据
 */
const loadMonthData = async () => {
  try {
    console.log('加载月度数据:', currentMonth.value)

    uni.showLoading({ title: '加载中...' })

    const res = await wx.cloud.callFunction({
      name: 'getRecordsByDate',
      data: { date: currentMonth.value }
    })
    
    uni.hideLoading()
    
    if (res.result.success) {
      records.value = res.result.data || []
      console.log('月度记录:', records.value)
      
      // 按用户分组
      groupRecordsByUser()
    } else {
      console.log('查询失败:', res.result)
      uni.showToast({ 
        title: res.result.error || '查询失败', 
        icon: 'none' 
      })
    }
  } catch (error) {
    console.log('调用失败:', error)
    uni.hideLoading()
    uni.showToast({ 
      title: '网络错误，请重试', 
      icon: 'none' 
    })
  }
}

/**
 * 按用户分组记录
 */
const groupRecordsByUser = () => {
  const groups = {}
  
  records.value.forEach(record => {
    const userName = record.name || '未知用户'
    
    if (!groups[userName]) {
      groups[userName] = {
        name: userName,
        records: [],
        total: 0
      }
    }
    
    groups[userName].records.push(record)
    groups[userName].total += parseFloat(getRecordTotal(record.description))
  })
  
  // 转换为数组并按总金额排序
  userGroups.value = Object.values(groups)
    .map(group => ({
      ...group,
      total: group.total.toFixed(2),
      records: group.records.sort((a, b) => b.date.localeCompare(a.date)) // 按日期倒序
    }))
    .sort((a, b) => parseFloat(b.total) - parseFloat(a.total)) // 按金额倒序
  
  console.log('用户分组:', userGroups.value)
}

// 页面加载时获取路由参数并加载数据
onMounted(() => {
  // 获取路由参数
  const pages = uni.getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options

  if (options && options.month) {
    currentMonth.value = options.month
    console.log('获取到月份参数:', currentMonth.value)
    loadMonthData()
  } else {
    uni.showToast({
      title: '缺少月份参数',
      icon: 'none'
    })
  }
})
</script>

<style>
/* 全局样式 */
page {
  background-color: #f8f9fa;
}

.container {
  padding: 24rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
  text-align: center;
  color: #2c3e50;
}

/* 月度概览样式 */
.month-overview {
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-label {
  font-size: 26rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}

.overview-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

/* 用户分组样式 */
.user-groups {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.user-group {
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* 用户头部样式 */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #007aff;
  color: #ffffff;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
}

.user-total {
  font-size: 30rpx;
  font-weight: 700;
}

/* 用户记录样式 */
.user-records {
  padding: 16rpx;
}

.record-item {
  margin-bottom: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #28a745;
}

.record-item:last-child {
  margin-bottom: 0;
}

/* 记录头部样式 */
.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.record-date {
  font-size: 28rpx;
  font-weight: 500;
  color: #495057;
}

.record-amount {
  font-size: 28rpx;
  font-weight: 700;
  color: #28a745;
}

/* 记录详情样式 */
.record-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 12rpx;
  background-color: #ffffff;
  border-radius: 6rpx;
}

.category-name {
  font-size: 26rpx;
  color: #6c757d;
}

.category-amount {
  font-size: 26rpx;
  font-weight: 500;
  color: #495057;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-text {
  font-size: 30rpx;
  color: #adb5bd;
}
</style>
