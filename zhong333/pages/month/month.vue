<template>
  <view class="container">
    <view class="title">{{ monthTitle }} 月度详情</view>
    
    <!-- 月度统计概览 -->
    <view class="month-overview">
      <view class="overview-item">
        <text class="overview-label">总金额</text>
        <text class="overview-value">¥{{ monthTotal }}</text>
      </view>
      <view class="overview-item">
        <text class="overview-label">记录数</text>
        <text class="overview-value">{{ totalRecords }}条</text>
      </view>
      <view class="overview-item">
        <text class="overview-label">记账天数</text>
        <text class="overview-value">{{ activeDays }}天</text>
      </view>
    </view>

    <!-- 按人员分组显示 -->
    <view class="user-groups">
      <view 
        v-for="(userGroup, index) in userGroups" 
        :key="userGroup.name"
        class="user-group"
      >
        <view class="user-header">
          <text class="user-name">{{ userGroup.name }}</text>
          <text class="user-total">¥{{ userGroup.total }}</text>
        </view>
        
        <!-- 该用户的分类汇总 -->
        <view class="user-categories">
          <view
            v-for="(categoryData, categoryName) in userGroup.categories"
            :key="categoryName"
            class="category-item"
          >
            <view class="category-info">
              <text class="category-name">{{ categoryName }}</text>
              <text class="category-count">{{ categoryData.count }}次</text>
            </view>
            <text class="category-total">¥{{ categoryData.total }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="userGroups.length === 0" class="empty-state">
      <text class="empty-text">该月暂无记录</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const records = ref([])
const userGroups = ref([])
const currentMonth = ref('')

// 计算属性
const monthTitle = computed(() => {
  if (!currentMonth.value) return ''
  const [year, monthNum] = currentMonth.value.split('-')
  return `${year}年${parseInt(monthNum)}月`
})

const monthTotal = computed(() => {
  return userGroups.value.reduce((sum, group) => {
    return sum + parseFloat(group.total || 0)
  }, 0).toFixed(2)
})

const totalRecords = computed(() => {
  return records.value.length
})

const activeDays = computed(() => {
  const days = new Set()
  records.value.forEach(record => {
    if (record.date) {
      days.add(record.date)
    }
  })
  return days.size
})





/**
 * 加载月度数据
 */
const loadMonthData = async () => {
  try {
    console.log('加载月度数据:', currentMonth.value)

    uni.showLoading({ title: '加载中...' })

    const res = await wx.cloud.callFunction({
      name: 'getRecordsByDate',
      data: { date: currentMonth.value }
    })
    
    uni.hideLoading()
    
    if (res.result.success) {
      records.value = res.result.data || []
      console.log('月度记录:', records.value)
      
      // 按用户分组
      groupRecordsByUser()
    } else {
      console.log('查询失败:', res.result)
      uni.showToast({ 
        title: res.result.error || '查询失败', 
        icon: 'none' 
      })
    }
  } catch (error) {
    console.log('调用失败:', error)
    uni.hideLoading()
    uni.showToast({ 
      title: '网络错误，请重试', 
      icon: 'none' 
    })
  }
}

/**
 * 按用户分组记录并按类型汇总
 */
const groupRecordsByUser = () => {
  const groups = {}

  records.value.forEach(record => {
    const userName = record.name || '未知用户'

    if (!groups[userName]) {
      groups[userName] = {
        name: userName,
        records: [],
        categories: {}, // 按类型汇总
        total: 0
      }
    }

    groups[userName].records.push(record)

    // 解析记录描述，按类型汇总
    try {
      const description = JSON.parse(record.description || '{}')
      Object.entries(description).forEach(([category, amount]) => {
        const numAmount = parseFloat(amount) || 0

        if (!groups[userName].categories[category]) {
          groups[userName].categories[category] = {
            total: 0,
            count: 0
          }
        }

        groups[userName].categories[category].total += numAmount
        groups[userName].categories[category].count += 1
        groups[userName].total += numAmount
      })
    } catch (error) {
      console.error('解析记录描述失败:', error)
    }
  })

  // 转换为数组并按总金额排序
  userGroups.value = Object.values(groups)
    .map(group => ({
      ...group,
      total: group.total.toFixed(2),
      // 将categories对象转换为数组，按金额排序
      categories: Object.entries(group.categories)
        .map(([name, data]) => ({
          name,
          total: data.total.toFixed(2),
          count: data.count
        }))
        .sort((a, b) => parseFloat(b.total) - parseFloat(a.total))
        .reduce((obj, item) => {
          obj[item.name] = {
            total: item.total,
            count: item.count
          }
          return obj
        }, {})
    }))
    .sort((a, b) => parseFloat(b.total) - parseFloat(a.total)) // 按金额倒序

  console.log('用户分组和类型汇总:', userGroups.value)
}

// 页面加载时获取路由参数并加载数据
onMounted(() => {
  // 获取路由参数
  const pages = uni.getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options

  if (options && options.month) {
    currentMonth.value = options.month
    console.log('获取到月份参数:', currentMonth.value)
    loadMonthData()
  } else {
    uni.showToast({
      title: '缺少月份参数',
      icon: 'none'
    })
  }
})
</script>

<style>
/* 全局样式 */
page {
  background-color: #f8f9fa;
}

.container {
  padding: 24rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
  text-align: center;
  color: #2c3e50;
}

/* 月度概览样式 */
.month-overview {
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-label {
  font-size: 26rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}

.overview-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

/* 用户分组样式 */
.user-groups {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.user-group {
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* 用户头部样式 */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #007aff;
  color: #ffffff;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
}

.user-total {
  font-size: 30rpx;
  font-weight: 700;
}

/* 用户分类汇总样式 */
.user-categories {
  padding: 16rpx;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 12rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #28a745;
  transition: all 0.2s ease;
}

.category-item:hover {
  background-color: #e9ecef;
  transform: translateX(4rpx);
}

.category-item:last-child {
  margin-bottom: 0;
}

/* 分类信息区域 */
.category-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.category-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4rpx;
}

.category-count {
  font-size: 24rpx;
  color: #6c757d;
}

/* 分类总金额 */
.category-total {
  font-size: 32rpx;
  font-weight: 700;
  color: #28a745;
  background-color: #d4edda;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid #c3e6cb;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-text {
  font-size: 30rpx;
  color: #adb5bd;
}
</style>
