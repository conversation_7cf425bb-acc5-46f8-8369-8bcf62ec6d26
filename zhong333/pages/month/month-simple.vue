<template>
  <view class="container">
    <view class="title">月度详情测试页面</view>
    <view class="content">
      <text>当前月份: {{ currentMonth }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const currentMonth = ref('')

onMounted(() => {
  const pages = uni.getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options
  
  if (options && options.month) {
    currentMonth.value = options.month
    console.log('获取到月份参数:', currentMonth.value)
  }
})
</script>

<style>
.container {
  padding: 40rpx;
}

.title {
  font-size: 36rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.content {
  font-size: 28rpx;
  text-align: center;
}
</style>
