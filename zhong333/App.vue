<script>
export default {
  onLaunch: function() {
    console.log('App Launch')
    if (!wx.cloud) {
      console.error('请使用 2.2.3 及以上基础库以支持云开发')
      return
    }
    wx.cloud.init({
      env: 'cloudbase-4gc39cjdb0cbb5e2',
      traceUser: true,
    })

    const cachedOpenId = wx.getStorageSync('openid')
    if (!cachedOpenId) {
      wx.cloud.callFunction({
        name: 'getOpenId',
        success: res => {
          wx.setStorageSync('openid', res.result.openid)
        },
        fail: err => {
          console.error('获取openid失败', err)
        }
      })
    } else {
      console.log('缓存的openid:', cachedOpenId)
    }
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  }
}
</script>

<style>
	/*每个页面公共css */
</style>
